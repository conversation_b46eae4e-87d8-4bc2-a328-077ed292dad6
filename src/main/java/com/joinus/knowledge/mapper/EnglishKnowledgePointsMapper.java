package com.joinus.knowledge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.knowledge.model.entity.EnglishKnowledgePoints;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

public interface EnglishKnowledgePointsMapper extends BaseMapper<EnglishKnowledgePoints> {

    List<EnglishKnowledgePoints> listByQuestionId(@Param("questionId") UUID questionId);

}
