package com.joinus.knowledge.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.enums.EducationalStage;
import com.joinus.knowledge.enums.MathQuestionReviewType;
import com.joinus.knowledge.model.dto.CandidateQuestion;
import com.joinus.knowledge.model.entity.MathQuestionReviewRecords;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.knowledge.model.vo.MathQuestionReviewRecordVO;
import com.joinus.knowledge.model.vo.MathQuestionReviewUserStatisticsVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_question_review_records(数学题审核记录表)】的数据库操作Mapper
* @createDate 2025-06-16 16:25:44
* @Entity com.joinus.knowledge.model.entity.MathQuestionReviewRecords
*/
public interface MathQuestionReviewRecordsMapper extends BaseMapper<MathQuestionReviewRecords> {
    List<Map<String,Object>> getInitialKeyPointStats(@Param("reviewType") String reviewType, @Param("educationalStage") EducationalStage educationalStage);

    List<CandidateQuestion> getAllCandidatesWithKeyPoints(@Param("username") String username, @Param("reviewType") String reviewType, @Param("educationalStage") EducationalStage educationalStage);

    Page<MathQuestionReviewRecordVO> listReviewRecordsByUser(@Param("page") Page<MathQuestionReviewRecordVO> page, @Param("username") String username, @Param("questionId") UUID questionId, @Param("status") String status, @Param("reviewType") MathQuestionReviewType reviewType,@Param("startDate") Date startDate,@Param("endDate") Date endDate);

    MathQuestionReviewUserStatisticsVO getUserStatistics(@Param("username") String username, @Param("reviewType") MathQuestionReviewType reviewType);
}




