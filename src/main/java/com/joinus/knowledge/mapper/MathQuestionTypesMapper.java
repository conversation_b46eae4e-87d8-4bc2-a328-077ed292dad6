package com.joinus.knowledge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathQuestionType;
import com.joinus.knowledge.model.param.PageQuestionTypeParam;
import com.joinus.knowledge.model.po.MathQuestionTypePO;
import com.joinus.knowledge.model.vo.MathQuestionTypeVO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_question_types】的数据库操作Mapper
* @createDate 2025-02-28 14:12:06
* @Entity com.joinus.knowledge.entity.MathQuestionTypes
*/
public interface MathQuestionTypesMapper extends BaseMapper<MathQuestionType> {

    @Delete("DELETE FROM math_question_types WHERE id = #{id}")
    void realDeleteById(@Param("id") UUID id);

    List<MathQuestionType> listByTextbookId(@Param("textbookId") UUID textbookId);

    List<MathQuestionType> listByQuestionId(@Param("id") UUID id);

    List<MathQuestionTypePO> listByQuestionIds(@Param("questionIds") List<UUID> questionIds);

    List<MathQuestionTypePO> listByQuestionIdsAndPublisher(@Param("questionIds") List<UUID> questionIds,
                                               @Param("publisher") PublisherType publisher);

    List<MathQuestionTypeVO> list(@Param("name") String name,
                                  @Param("grade") Integer grade,
                                  @Param("semester") Integer semester,
                                  @Param("publisher") PublisherType publisher,
                                  @Param("chapterId") UUID chapterId,
                                  @Param("chapterName") String chapterName,
                                  @Param("sectionId") UUID sectionId,
                                  @Param("sectionName") String sectionName);

    List<MathQuestionTypeVO> listByIds(@Param("ids") List<UUID> ids);

    List<MathQuestionTypeVO> listByIdsAndPublisher(@Param("ids") List<UUID> ids,
                                                   @Param("publisher") PublisherType publisher,
                                                   @Param("grade") Integer grade,
                                                   @Param("semester") Integer semester);

    List<MathQuestionTypeVO> listEnableAiQuestionCountByKnowledgePointIds(@Param("knowledgePointIds") List<UUID> knowledgePointIds);

    List<MathQuestionTypeVO> listBySectionIds(@Param("sectionIds") List<UUID> list);

    Page<MathQuestionTypeVO> pageQuery(Page<MathQuestionTypeVO> pageParam, @Param("param") PageQuestionTypeParam param);
}




