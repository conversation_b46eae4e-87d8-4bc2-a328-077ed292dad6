package com.joinus.knowledge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.model.entity.EnglishExam;
import com.joinus.knowledge.model.param.EnglishExamPageQueryParam;
import com.joinus.knowledge.model.vo.EnglishExamVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【en_flow_exam(英语试卷表)】的数据库操作Mapper
 * @createDate 2025-09-01 18:15:37
 * @Entity com.joinus.knowledge.model.entity.EnglishExam
 */
public interface EnglishExamMapper extends BaseMapper<EnglishExam> {

    IPage<EnglishExamVO> pageQuery(Page<EnglishExamVO> page, @Param("param") EnglishExamPageQueryParam param);

    List<String> listSchool(@Param("school") String school);
}
