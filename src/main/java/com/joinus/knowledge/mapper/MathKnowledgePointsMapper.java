package com.joinus.knowledge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathKnowledgePoint;
import com.joinus.knowledge.model.param.PageKnowledgePointParam;
import com.joinus.knowledge.model.po.MathKnowledgePointPO;
import com.joinus.knowledge.model.vo.MathKeyPointVO;
import com.joinus.knowledge.model.vo.MathKnowledgePointVO;
import com.joinus.knowledge.model.vo.SectionKnowledgePointVO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_knowledge_points】的数据库操作Mapper
* @createDate 2025-02-28 14:12:06
* @Entity com.joinus.knowledge.entity.MathKnowledgePoints
*/
public interface MathKnowledgePointsMapper extends BaseMapper<MathKnowledgePoint> {

    List<MathKnowledgePoint> listByGradeAndSemester(@Param("grade") Integer grade, @Param("semester") Integer semester, @Param("publisher") PublisherType publisher);

    List<MathKnowledgePoint> listByQuestionId(@Param("questionId") UUID questionId);

    List<MathKnowledgePoint> listByPublisher(@Param("publisher") String publisher);

    @Delete("DELETE FROM math_knowledge_points WHERE id = #{id}")
    void realDeleteById(@Param("id") UUID id);

    List<MathKnowledgePointVO> list(@Param("name") String name,
                                    @Param("grade") Integer grade,
                                    @Param("semester") Integer semester,
                                    @Param("publisher") PublisherType publisher,
                                    @Param("chapterId") UUID chapterId,
                                    @Param("chapterName") String chapterName,
                                    @Param("sectionId") UUID sectionId,
                                    @Param("sectionName") String sectionName);


    List<MathKnowledgePoint> listByTextbookId(@Param("textbookId") UUID textbookId);

    List<MathKnowledgePointPO> listByQuestionIds(@Param("questionIds") List<UUID> questionIds);

    List<MathKnowledgePointPO> listByQuestionIdsAndPublisher(@Param("questionIds") List<UUID> questionIds,
                                                             @Param("publisher") PublisherType publisher);

    List<MathKnowledgePointVO> listQuestionTypeByKnowledgePointIds(@Param("knowledgePointIds") List<UUID> knowledgePointIds);

    List<SectionKnowledgePointVO> listSectionKnowledgePointByKnowledgeIds(@Param("knowledgePointIds") List<UUID> knowledgePointIds,
                                                                          @Param("grade") Integer  grade,
                                                                          @Param("semester") Integer semester,
                                                                          @Param("publisher") PublisherType publisher);

    List<MathKnowledgePointVO> listBySectionIds(@Param("sectionIds") List<UUID> sectionIds);

    List<MathKnowledgePointVO> listByIds(@Param("ids") List<UUID> ids);

    List<MathKnowledgePointVO> listKnowledgePointByQuestionId(@Param("questionId") UUID questionId,
                                                            @Param("grade") Integer grade,
                                                            @Param("semester") Integer semester,
                                                            @Param("publisher") PublisherType publisher);

    List<MathKnowledgePointVO> listByIdsAndPublisher(@Param("ids") List<UUID> ids,
                                                     @Param("publisher") PublisherType publisher,
                                                     @Param("grade") Integer grade,
                                                     @Param("semester") Integer semester);


    List<MathKnowledgePointVO> listEnableAiQuestionCountByKnowledgePointIds(@Param("knowledgePointIds") List<UUID> knowledgePointIds);

    Page<MathKnowledgePointVO> pageQuery(Page<MathKnowledgePointVO> pageParam,@Param("param") PageKnowledgePointParam param);

    List<MathKeyPointVO> getPrimarySchoolKeyPoints();

    List<MathKeyPointVO> listMiddleSchoolKnowledgePoints(@Param("publisher") PublisherType publisher);

    List<MathKeyPointVO> listHighSchoolKnowledgePoints(@Param("publisher") PublisherType publisher);

}




