package com.joinus.knowledge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathCatalogNodes;
import com.joinus.knowledge.model.vo.MathCatalogNodeVO;
import com.joinus.knowledge.model.vo.MathChapterVO;
import com.joinus.knowledge.model.vo.MathSectionVO;
import com.joinus.knowledge.model.vo.SectionKeypointVO;
import com.joinus.knowledge.model.vo.SectionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_catalog_nodes(数学目录节点表)】的数据库操作Mapper
* @createDate 2025-07-30 17:33:40
* @Entity com.joinus.knowledge.model.entity.MathCatalogNodes
*/
@Mapper
public interface MathCatalogNodesMapper extends BaseMapper<MathCatalogNodes> {

    List<MathCatalogNodeVO> list(@Param("publisher") PublisherType publisher, @Param("grade") Integer grade, @Param("semester") Integer semester);

    /**
     * 查询章节列表（用于替代原MathChaptersMapper.list方法）
     */
    List<MathChapterVO> listChapters(@Param("name") String name,
                                     @Param("grade") Integer grade,
                                     @Param("semester") Integer semester,
                                     @Param("publisher") PublisherType publisher);

    /**
     * 根据教材ID查询所有小节（叶节点）
     */
    List<SectionVO> listAllSectionsByBookId(@Param("bookId") UUID bookId);

    /**
     * 查询小节列表（用于替代原MathSectionMapper.list方法）
     */
    List<MathSectionVO> listSections(@Param("name") String name,
                                     @Param("grade") Integer grade,
                                     @Param("semester") Integer semester,
                                     @Param("publisher") PublisherType publisher,
                                     @Param("chapterName") String chapterName,
                                     @Param("chapterId") UUID chapterId);

    /**
     * 根据页码查询小节
     */
    List<MathCatalogNodes> getByPageNo(@Param("pageNo") Integer pageNo,
                                       @Param("textbookId") UUID textbookId,
                                       @Param("sectionId") UUID sectionId);

    /**
     * 根据小节ID查询关键点
     */
    List<SectionKeypointVO> listKeypointsById(@Param("sectionId") UUID sectionId);

    /**
     * 根据出版社查询所有小节
     */
    List<SectionVO> listAllSectionsByPublisher(@Param("publisher") PublisherType publisher);

    /**
     * 根据条件查询小节
     */
    List<SectionVO> listSectionsByCondition(@Param("grade") Integer grade,
                                            @Param("publisher") PublisherType publisher,
                                            @Param("semester") Integer semester);

    List<MathCatalogNodes> listByTextbookId(@Param("textbookId") UUID textbookId);
}




