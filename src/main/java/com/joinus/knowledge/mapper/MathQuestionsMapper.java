package com.joinus.knowledge.mapper;

import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.enums.QuestionSourceType;
import com.joinus.knowledge.model.dto.QuestionKnowledgePointDTO;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.joinus.knowledge.model.entity.QuestionKnowledgePoint;
import com.joinus.knowledge.model.entity.QuestionTypesMapping;
import com.joinus.knowledge.model.param.PageQuestionParam;
import com.joinus.knowledge.model.po.AIGeneratedQuestionCount;
import com.joinus.knowledge.model.po.ExamQuestionPO;
import com.joinus.knowledge.model.po.QuestionPublishInfoPO;
import com.joinus.knowledge.model.po.TrainingQuestionPO;
import com.joinus.knowledge.model.vo.MathQuestionGraphicsScriptVO;
import com.joinus.knowledge.model.vo.MathQuestionVO;
import com.joinus.knowledge.model.vo.QuestionDetailVO;
import com.joinus.knowledge.model.vo.QuestionWithLatestAnswerVO;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_questions】的数据库操作Mapper
* @createDate 2025-02-28 14:12:06
* @Entity com.joinus.knowledge.entity.MathQuestions
*/
public interface MathQuestionsMapper extends BaseMapper<MathQuestion> {

    List<QuestionDetailVO> listQuestionAndAnswerByQuestionType(UUID keyPointId);

    List<QuestionDetailVO> listQuestionAndAnswerByKnowledgePoint(UUID keyPointId);

    QuestionDetailVO getDetailById(UUID uuid);

    List<ExamQuestionPO> listQuestionByIds(@Param("questionIdList") List<UUID> questionIdList);

    List<QuestionKnowledgePoint> listTrainingQuestionIdsByKnowledgePoints(@Param("knowledgePointIds") List<UUID> knowledgePointIds, @Param("questionCount") Integer questionCount);

    List<QuestionWithLatestAnswerVO> listNoKnowledgeDomainLabelQuestions(@Param("count") Integer count);

    List<QuestionWithLatestAnswerVO> listMathQuestionByKnowledgeDomain(@Param("labelName")String labelName,@Param("count") Integer count);

    Page<MathQuestionVO> pageWithCustomQueryV3(Page<MathQuestionVO> pageParam, @Param("param") PageQuestionParam param);

    List<QuestionTypesMapping> listQuestionIdsByQuestionTypes(@Param("questionTypeIds") List<UUID> questionTypeIds);

    List<QuestionTypesMapping> listQuestionIdsByQuestionTypes(@Param("questionTypeIds") List<UUID> questionTypeIds, @Param("questionCount") Integer questionCount);

    Page<MathQuestionGraphicsScriptVO> queryQuestionGraphicsScriptByUser(@Param("page") Page<MathQuestionGraphicsScriptVO> page, @Param("username") String username, @Param("labelName") String labelName, @Param("questionId") UUID questionId, @Param("status") String status, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    List<QuestionPublishInfoPO> listPublishInfo(UUID id);

    List<MathQuestion> listMultiKnowledgePointQuestionFromBook();

    List<MathQuestion> listMultiQuestionTypesMappingFromBook();

    List<QuestionDetailVO> getDetailByIds(@Param("ids") List<UUID> ids);

    List<QuestionDetailVO> listQuestionAndAnswerByIds(@Param("ids") List<UUID> ids);

    List<AIGeneratedQuestionCount> listNotEnoughAIGeneratedQuestionCount(@Param("count") int count);

    List<AIGeneratedQuestionCount> listConditionalNotEnoughAIGeneratedQuestionCount();

    List<QuestionKnowledgePointDTO> listPastExamPaperQuestions(@Param("knowledgePointIds") List<UUID> knowledgePointIds,
                                                               @Param("questionCount") Integer questionCount,
                                                               @Param("source") QuestionSourceType source);

    List<QuestionDetailVO> listExamQuestionDetailByExamId(@Param("examId") UUID examId);

    QuestionDetailVO getExamInfo(@Param("id") UUID id);

    List<MathQuestion> listNoneKnowledgePointsExamQuestions();

    List<MathQuestion> listAvailableAIQuestionsByKnowledgePointId(@Param("knowledgePointId") UUID knowledgePointId);

    List<MathQuestion> listAvailableAIQuestionsByQuestionTypeId(@Param("questionTypeId") UUID questionTypeId);

    List<TrainingQuestionPO> listMathHolidayTrainingQuestions(@Param("sectionId") UUID sectionIds,
                                                              @Param("chapterId") UUID chapterId);

    List<AIGeneratedQuestionCount> listPrimarySchoolBaseQuestionByKnowledgePoints(@Param("count") int count);

    List<AIGeneratedQuestionCount> listPrimarySchoolBaseQuestionByQuestionTypes(@Param("count") int count);

}
