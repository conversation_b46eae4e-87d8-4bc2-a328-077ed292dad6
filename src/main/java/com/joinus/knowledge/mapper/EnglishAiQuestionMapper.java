package com.joinus.knowledge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.model.entity.EnglishAiQuestion;
import com.joinus.knowledge.model.param.EnglishAiQuestionPageQueryParam;
import com.joinus.knowledge.model.vo.EnglishAiQuestionVO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【english_ai_question(英语ai试题表)】的数据库操作Mapper
 * @createDate 2025-09-01 18:15:37
 * @Entity com.joinus.knowledge.model.entity.EnglishAiQuestion
 */
public interface EnglishAiQuestionMapper extends BaseMapper<EnglishAiQuestion> {

    IPage<EnglishAiQuestionVO> pageQuery(Page<EnglishAiQuestionVO> page, @Param("param") EnglishAiQuestionPageQueryParam param);

}
