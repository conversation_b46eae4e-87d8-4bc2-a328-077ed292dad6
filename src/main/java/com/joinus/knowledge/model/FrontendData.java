package com.joinus.knowledge.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FrontendData implements Serializable {

    private List<WordData> words;
    private List<PhonemeData> phonemes;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WordData implements Serializable {
        private String word;
        private Double start_time;
        private Double end_time;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PhonemeData implements Serializable {
        private String phone;
        private Double start_time;
        private Double end_time;
    }

}
