package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * 
 * @TableName math_handout_slideshow_presentation_files
 */
@TableName(value ="math_handout_slideshow_presentation_files")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class MathHandoutSlideshowPresentationFile implements Serializable {

    /**
     * Primary key - UUID
     */
    @TableId(type = IdType.INPUT)
    private UUID id;

    /**
     * 
     */
    private UUID mathHandoutSlideshowPresentationId;

    /**
     * 文件类型 AUDIO,
     */
    private String type;

    /**
     * 文件id
     */
    private UUID fileId;

    /**
     * Creation time - auto-filled when inserting
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    /**
     * Update time - auto-filled when inserting or updating
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;

}