package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import lombok.Data;

@TableName(value = "english_knowledge_points")
@Data
public class EnglishKnowledgePoints extends BaseEntity {

    /**
     * 题目内容
     */
    @TableField(value = "name")
    private String name;

}
