package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import com.joinus.knowledge.config.typehandler.JsonbTypeHandler;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

/**
 * 讲义幻灯片展示表
 * @TableName math_handout_slideshow_presentations
 */
@TableName(value ="math_handout_slideshow_presentations")
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Data
public class MathHandoutSlideshowPresentation extends BaseEntity {

    /**
     * ppt文件对应的html文件列表
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private String pptHtmls;

    /**
     * 讲义id
     */
    private UUID handoutId;
}