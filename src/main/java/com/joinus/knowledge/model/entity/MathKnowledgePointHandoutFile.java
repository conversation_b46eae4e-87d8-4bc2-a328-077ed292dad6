package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.joinus.knowledge.enums.KnowledgePointHandoutFileType;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * 知识点讲义文件表
 * @TableName math_knowledge_point_handout_files
 */
@TableName(value ="math_knowledge_point_handout_files")
@Data
@Builder
public class MathKnowledgePointHandoutFile implements Serializable {

    /**
     * Primary key - UUID
     */
    @TableId(type = IdType.INPUT)
    private UUID id;

    /**
     * 讲义id
     */
    private UUID handoutId;

    /**
     * 文件类型 PDF、MP4
     */
    private KnowledgePointHandoutFileType type;

    /**
     * 文件id
     */
    private UUID fileId;

    /**
     * Creation time - auto-filled when inserting
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    /**
     * Update time - auto-filled when inserting or updating
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;

}