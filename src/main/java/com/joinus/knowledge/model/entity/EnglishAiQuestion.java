package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import com.joinus.knowledge.config.typehandler.JsonbTypeHandler;
import lombok.Data;

import java.util.List;

@TableName(value = "english_ai_question")
@Data
public class EnglishAiQuestion extends BaseEntity {

    /**
     * 题目内容
     */
    @TableField(value = "content")
    private String content;
    /**
     * 题目类型
     */
    @TableField(value = "type")
    private String type;
    /**
     * 难度等级：1-5
     */
    @TableField(value = "difficulty")
    private Integer difficulty;
    /**
     * 来源 1.AI
     */
    @TableField(value = "source_type")
    private Integer sourceType;
    /**
     * 是否启用：0、否，1、是
     */
    @TableField(value = "enabled")
    private Integer enabled;
    /**
     * 答案
     */
    @TableField(value = "answer")
    private String answer;
    /**
     * 答案解析
     */
    @TableField(value = "analysis")
    private String analysis;
    /**
     * 年级
     */
    @TableField(value = "grade")
    private Integer grade;
    /**
     * 学期：上学期、下学期
     */
    @TableField(value = "semester")
    private String semester;
    /**
     * 题目
     */
    @TableField(value = "title")
    private String title;
    /**
     * 题目
     */
    @TableField(value = "options", typeHandler = JsonbTypeHandler.class)
    private String options;

}
