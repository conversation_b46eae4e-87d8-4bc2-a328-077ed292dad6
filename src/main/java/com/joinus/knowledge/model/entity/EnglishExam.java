package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import com.joinus.knowledge.config.typehandler.JsonbTypeHandler;
import lombok.*;

@TableName(value = "english_flow_exam")
@Data
public class EnglishExam extends BaseEntity {

    /**
     * 试卷名称
     */
    @TableField(value = "name")
    private String name;
    /**
     * 试卷来源：1.用户上传
     */
    @TableField(value = "source_type")
    private Integer sourceType;
    /**
     * 试卷类型：期中、期末、月考、其他
     */
    @TableField(value = "type")
    private String type;
    /**
     * 所属年份
     */
    @TableField(value = "year")
    private Integer year;
    /**
     * 所属学校
     */
    @TableField(value = "school")
    private String school;
    /**
     * 所属区域
     */
    @TableField(value = "area")
    private String area;
    /**
     * 所属年级
     */
    @TableField(value = "grade")
    private Integer grade;
    /**
     * 学期：上学期、下学期
     */
    @TableField(value = "semester")
    private String semester;
    /**
     * oss图片地址
     */
    @TableField(value = "oss_urls", typeHandler = JsonbTypeHandler.class)
    private String ossUrls;

}
