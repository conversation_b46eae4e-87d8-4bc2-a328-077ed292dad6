package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.UUID;

/**
 * 讲义表
 * @TableName math_knowledge_point_handouts
 */
@TableName(value ="math_knowledge_point_handouts")
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Data
public class MathKnowledgePointHandout extends BaseEntity {

    /**
     * 知识点id
     */
    private UUID knowledgePointId;

    /**
     * 讲义内容，markdown格式
     */
    private String contentMarkdown;

    /**
     * 审核状态 通过：APPROVED
     */
    private String reviewStatus;

    /**
     * 审核人
     */
    private String reviewUser;

    /**
     * 审核时间
     */
    private Date reviewedAt;

}