package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.UUID;

/**
 * 题目与答案的关联关系表
 * @TableName math_question_answers
 */
@Data
@TableName(value ="math_question_answers")
public class QuestionAnswerRelation implements Serializable {
    /**
     * 题目ID
     */
    private UUID questionId;

    /**
     * 答案ID
     */
    private UUID answerId;

    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}