package com.joinus.knowledge.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.joinus.knowledge.config.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 
 * @TableName math_answers
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value ="math_answers")
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class MathAnswer extends BaseEntity {
    /**
     * 答案
     */
    private String answer;

    /**
     * 答案解析
     */
    private String content;

}
