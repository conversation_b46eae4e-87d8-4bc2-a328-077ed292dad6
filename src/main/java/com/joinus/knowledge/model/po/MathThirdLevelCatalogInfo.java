package com.joinus.knowledge.model.po;

import lombok.Data;

import java.util.List;
/**
 * 第三级目录信息 （通用节点）
 * <AUTHOR>

 */
@Data
public class MathThirdLevelCatalogInfo {
    private String thirdLevelCatalogName;
    //通用级别名称
    private String levelCatalogName;
    private Integer sortNo;
    private Integer startPage;
    private Integer endPage;
    private List<KeyPointInfo> keyPoints;
    private List<MathThirdLevelCatalogInfo> children;
}
