package com.joinus.knowledge.model.po;

import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.vo.MathKnowledgePointVO;
import com.joinus.knowledge.model.vo.MathPastExamPaperQuestionVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MathKnowledgePointPO implements Serializable {

    private UUID id;

    private String name;

    private Integer grade;

    private Integer semester;

    private PublisherType publisher;

    private UUID questionId;

    private UUID chapterId;

    private String chapterName;

    private Integer chapterSortNo;

    private UUID sectionId;

    private String sectionName;

    private Integer sectionSortNo;

    private Integer sortNo;

    private Boolean examPoint;

    private String fullPath;

    private UUID catalogNodeId;

    public MathKnowledgePointVO convertToVO () {
        return MathKnowledgePointVO.builder()
                .id(id)
                .name(name)
                .grade(grade)
                .semester(semester)
                .publisher(publisher)
                .chapterId(chapterId)
                .chapterName(chapterName)
                .chapterSortNo(chapterSortNo)
                .sectionId(sectionId)
                .sectionName(sectionName)
                .sectionSortNo(sectionSortNo)
                .fullPath(fullPath)
                .catalogNodeId(catalogNodeId)
                .build();
    }

    public MathPastExamPaperQuestionVO toMathPastExamPaperQuestionVO() {
        return MathPastExamPaperQuestionVO.builder()
                .id(questionId)
                .build();
    }
}
