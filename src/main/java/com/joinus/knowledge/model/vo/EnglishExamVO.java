package com.joinus.knowledge.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Data
@Schema(description = "英语试卷分页查询结果")
public class EnglishExamVO {
    @Schema(description = "试卷ID", example = "e813da65-24ff-442f-92c0-cf66767ea25d")
    private UUID id;
    @Schema(description = "试卷名称", example = "2024-2025学年河南省郑州市七年级下学期期末试卷")
    private String name;
    @Schema(description = "试卷来源", example = "1.用户上传")
    private Integer sourceType;
    @Schema(description = "试卷类型：期中、期末、月考、其他", example = "期中")
    private String type;
    @Schema(description = "所属年份", example = "2025")
    private Integer year;
    @Schema(description = "所属区域", example = "郑州市中原区")
    private String area;
    @Schema(description = "所属学校")
    private String school;
    @Schema(description = "所属年级")
    private Integer grade;
    @Schema(description = "学期：上学期、下学期", example = "2")
    private String semester;
    @Schema(description = "题目数量")
    private Integer questionCount;
    @Schema(description = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;
    @Schema(description = "试卷图片列表")
    private List<String> images;
}
