package com.joinus.knowledge.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MathHandoutSlideshowVO implements Serializable {

    @Schema(description = "幻灯片ID")
    private UUID slideshowId;

    @Schema(description = "讲义ID")
    private UUID handoutId;

    @Schema(description = "知识点ID")
    private UUID knowledgePointId;

    @Schema(description = "知识点名称")
    private String knowledgePointName;

    @Schema(description = "幻灯片内容")
    private List<MathHandoutSlideshowPageVO> slideshowPages;
}
