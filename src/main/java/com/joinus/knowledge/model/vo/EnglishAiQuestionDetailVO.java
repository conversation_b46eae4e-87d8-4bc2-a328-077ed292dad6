package com.joinus.knowledge.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.UUID;

@Data
@Schema(description = "英语题库详情查询结果")
public class EnglishAiQuestionDetailVO {

    @Schema(description = "题目ID", example = "e813da65-24ff-442f-92c0-cf66767ea25d")
    private UUID id;
    @Schema(description = "题型", example = "选择题")
    private String type;
    @Schema(description = "题干", example = "Who joined the activities?")
    private String title;
    @Schema(description = "答案", example = "A")
    private String answer;
    @Schema(description = "解析")
    private String analysis;
    @Schema(description = "难度：1-5", example = "5")
    private Integer difficulty;
    @Schema(description = "状态：0、不可用，1、使用中", example = "0")
    private Integer enabled;
    @Schema(description = "知识点", example = "细节理解+信息匹配")
    private String knowledgePoints;
    @Schema(description = "选项列表，json字符串")
    private String options;

}
