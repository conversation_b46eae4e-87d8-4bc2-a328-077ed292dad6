package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.enums.PublisherType;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@Schema(description = "数学题型")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MathQuestionTypeVO implements Serializable {

    @Schema(description = "题型id", implementation = UUID.class, example = "d0a0d0a0-d0a0-d0")
    private UUID id;

    @Schema(description = "题型名称", implementation = String.class, example = "1")
    private String name;

    @Schema(description = "年级", implementation = Integer.class, example = "1")
    private Integer grade;

    @Schema(description = "学期", implementation = Integer.class, example = "1")
    private Integer semester;

    @Schema(description = "出版社", implementation = PublisherType.class, example = "1")
    private PublisherType publisher;

    @Schema(description = "出版社描述", implementation = String.class, example = "1")
    private String publisherDescription;

    @ArraySchema(schema = @Schema(description = "题型列表", implementation = QuestionTypeVO.class))
    private List<QuestionTypeVO> questionTypes;

    @Schema(description = "章id", implementation = UUID.class, example = "d0a0d0a0-d0a0-d0")
    private UUID chapterId;

    @Schema(description = "章名称", implementation = String.class, example = "1")
    private String chapterName;

    @Schema(description = "章排序号", implementation = Integer.class, example = "1")
    private Integer chapterSortNo;

    @Schema(description = "节id", implementation = UUID.class, example = "d0a0d0a0-d0a0-d0")
    private UUID sectionId;

    @Schema(description = "节排序号", implementation = Integer.class, example = "1")
    private Integer sectionSortNo;

    @Schema(description = "节名称", implementation = String.class, example = "1")
    private String sectionName;

    @Schema(description = "页码", implementation = Integer.class, example = "1")
    private Integer pageIndex;

    private Integer enableAiQuestionCount;

    @Schema(description = "教材id", implementation = UUID.class, example = "d0a0d0a0-d0a0-d0")
    private UUID textbookId;

    @Schema(description = "目录路径", implementation = String.class)
    private String fullPath;

    @Schema(description = "目录id", implementation = Boolean.class)
    private UUID catalogNodeId;

    @Schema(description = "题型类别", implementation = String.class)
    private String category;

    @Schema(description = "原名", implementation = String.class)
    private String originalName;

    public String getPublisherDescription() {
        return null == publisher ? null : publisher.getValue();
    }
}
