package com.joinus.knowledge.model.vo;

import com.joinus.knowledge.model.FrontendData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MathHandoutVoiceVO implements Serializable {

    @Schema(description = "音色类型")
    private String type;
    @Schema(description = "音色名称")
    private String name;
    @Schema(description = "音频ossKey")
    private String ossKey;
    @Schema(description = "音频ossUrl")
    private String ossUrl;
    @Schema(description = "音频时长")
    private Integer duration;
    @Schema(description = "音频前端数据（字幕使用）")
    private FrontendData frontend;
    @Schema(description = "性别")
    private String sex;
    @Schema(description = "年龄段")
    private String ageType;
}
