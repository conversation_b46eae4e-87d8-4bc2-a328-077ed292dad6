package com.joinus.knowledge.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MathHandoutSlideshowPageVO implements Serializable {

    @Schema(description = "幻灯片页码")
    private Integer index;

    @Schema(description = "本页幻灯片内容")
    private String html;

    @Schema(description = "本页幻灯片文字旁白")
    private String narration;

    @Schema(description = "本页幻灯片音频(不同音色)")
    private List<MathHandoutVoiceVO> voices;
}
