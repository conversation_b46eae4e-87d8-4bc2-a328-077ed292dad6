package com.joinus.knowledge.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Schema(description = "英语题库分页查询结果")
public class EnglishAiQuestionVO {

    @Schema(description = "题目ID", example = "e813da65-24ff-442f-92c0-cf66767ea25d")
    private UUID id;
    @Schema(description = "题目来源", example = "1.AI生成")
    private Integer sourceType;
    @Schema(description = "年级")
    private Integer grade;
    @Schema(description = "学期", example = "下学期")
    private String semester;
    @Schema(description = "题型", example = "选择题")
    private String type;
    @Schema(description = "试题内容", example = "What was the purpose of “Reading Week”?")
    private String content;
    @Schema(description = "难度：1-5", example = "5")
    private Integer difficulty;
    @Schema(description = "是否启用：0、不可用，1、使用中", example = "0")
    private Integer enabled;
    @Schema(description = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;
    @Schema(description = "题目审查结果", example = "false")
    private boolean status;
    @Schema(description = "题目审查原因", example = "缺失答案")
    private String explain;
    @Schema(description = "错误类型", example = "生产失败")
    private String errorType;
    @Schema(description = "错误原因", example = "生产失败")
    private String severity;

}
