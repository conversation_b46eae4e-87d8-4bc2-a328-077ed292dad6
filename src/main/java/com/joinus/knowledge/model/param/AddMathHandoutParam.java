package com.joinus.knowledge.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddMathHandoutParam implements Serializable {

    private UUID knowledgePointId;

    private List<MathHandoutSlideParam> slideshowPages;
}
