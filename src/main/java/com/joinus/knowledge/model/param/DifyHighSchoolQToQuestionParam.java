package com.joinus.knowledge.model.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class DifyHighSchoolQToQuestionParam {
    @JsonProperty("question_id")
    private String questionId;
    
    @JsonProperty("question_type_id")
    private String questionTypeId;

    @JsonProperty("question_content")
    private String questionContent;

    @JsonProperty("answer")
    private String answer;

    @JsonProperty("analysis")
    private String analysis;
    //题所属的目录
    @JsonProperty("full_path")
    private String fullPath;
    //题所属的标题
    @JsonProperty("question_title")
    private String questionTitle;

    @JsonProperty("question_image")
    private List<DifyImageParam> questionImage;

}
