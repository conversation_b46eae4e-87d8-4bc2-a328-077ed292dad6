package com.joinus.knowledge.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "英语试卷分页查询参数")
public class EnglishExamPageQueryParam extends PageParam {
    @Schema(description = "试卷名称", example = "2024-2025学年河南省郑州市七年级下学期期末试卷")
    private String name;
    @Schema(description = "试卷来源", example = "1.用户上传")
    private Integer sourceType;
    @Schema(description = "试卷类型：期中、期末、月考、其他", example = "期中")
    private String type;
    @Schema(description = "年份", example = "2025")
    private Integer year;
    @Schema(description = "所属区域", example = "郑州市中原区")
    private String area;
    @Schema(description = "所属学校")
    private String school;
    @Schema(description = "年级")
    private Integer grade;
    @Schema(description = "学期：上学期、下学期", example = "上学期")
    private String semester;
}
