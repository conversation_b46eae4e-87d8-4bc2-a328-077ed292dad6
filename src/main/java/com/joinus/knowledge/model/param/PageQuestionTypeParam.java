package com.joinus.knowledge.model.param;

import com.joinus.knowledge.enums.PublisherType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class PageQuestionTypeParam extends PageParam{
    private UUID id;
    private UUID textbookId;
    private PublisherType publisher;
    private Integer grade;
    private Integer semester;
    private UUID catalogNodeId;
    private String name;
    private String originalName;
    private String category;
}
