package com.joinus.knowledge.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
public class MathGeneratedQuestionsParam {

    @Schema(description = "题型id", implementation = UUID.class)
    private UUID questionTypeId;

    @Schema(description = "知识点id", implementation = UUID.class)
    private UUID knowledgePointId;

    @Schema(description = "母题id", implementation = UUID.class)
    private UUID baseQuestionId;

    @Schema(description = "生成的题目列表")
    private List<MathGeneratedQuestion> questions;
}
