package com.joinus.knowledge.model.param;

import com.joinus.knowledge.model.entity.MathQuestion;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Data
public class MathQuestionParam extends MathQuestion {

    @NotBlank(message = "keyPointType不能为空")
    private String keyPointType;

    @NotNull(message = "keyPointId不能为空")
    private UUID keyPointId;
}
