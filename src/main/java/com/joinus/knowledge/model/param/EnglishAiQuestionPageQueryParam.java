package com.joinus.knowledge.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "英语题库分页查询参数")
public class EnglishAiQuestionPageQueryParam extends PageParam {
    @Schema(description = "试题内容", example = "What was the purpose ")
    private String content;
    @Schema(description = "试卷来源", example = "1.AI")
    private Integer sourceType;
    @Schema(description = "年级")
    private Integer grade;
    @Schema(description = "学期：上学期、下学期", example = "上学期")
    private String semester;
    @Schema(description = "题型：选择题", example = "选择题")
    private String type;
    @Schema(description = "题目难度：1-5", example = "1")
    private Integer difficulty;
    @Schema(description = "使用状态：0、不可用，1、使用中", example = "1")
    private Integer enabled;
}
