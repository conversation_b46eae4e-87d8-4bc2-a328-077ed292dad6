package com.joinus.knowledge.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
@Schema(description = "英语题目启用/禁用参数")
public class EnableOrDisableEnglishAiQuestionParam {

    @NotEmpty(message = "要启用或禁用的题目不能为空")
    @Schema(description = "要启用或禁用的题目ID列表")
    private List<UUID> uuidList;

}
