package com.joinus.knowledge.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "英语试卷修改参数")
public class UpdateEnglishExamParam {

    @NotEmpty(message = "试卷名称不能为空")
    @Schema(description = "试卷名称", example = "2024-2025学年河南省郑州市七年级下学期期末试卷")
    private String name;
    @NotNull(message = "试卷来源不能为空")
    @Schema(description = "试卷来源", example = "1.用户上传")
    private Integer sourceType;
    @NotEmpty(message = "试卷类型不能为空")
    @Schema(description = "试卷类型：期中、期末、月考、其他", example = "期中")
    private String type;
    @NotNull(message = "所属年份不能为空")
    @Schema(description = "所属年份", example = "2025")
    private Integer year;
    @NotEmpty(message = "所属学校不能为空")
    @Schema(description = "所属学校")
    private String school;
    @NotNull(message = "所属年级不能为空")
    @Schema(description = "所属年级")
    private Integer grade;
    @NotEmpty(message = "所属学期不能为空")
    @Schema(description = "所属学期：上学期、下学期", example = "上学期")
    private String semester;

}
