package com.joinus.knowledge.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
public enum MathLabelTypeEnum {

    KNOWLEDGE_DOMAIN("知识领域"),
    CORRECTNESS("正确性"),
    ILLUSTRATION_NEED("配图"),
    AI_CONSISTENCY_VERIFICATION("AI一致性"),
    LATEX_SYNTAX("数学公式"),
    DEFAULT("未知类型"),
    GENERATED_METHOD("生题方式");

    @Schema(description = "标签类型描述", implementation = String.class, example = "知识领域")
    private final String description;


    MathLabelTypeEnum(String value) {
        this.description = value;
    }

    public static MathLabelTypeEnum ofType(String type) {
        for (MathLabelTypeEnum label : MathLabelTypeEnum.values()) {
            if (label.name().equals(type)) {
                return label;
            }
        }
        return DEFAULT;
    }
}
