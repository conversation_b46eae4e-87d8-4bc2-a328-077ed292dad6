package com.joinus.knowledge.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
public enum PublisherType {
    BEI_SHI_DA("北师大"),
    HUA_SHI_DA("华师大"),
    REN_JIAO("人教"),
    SU_JIAO("苏教"),
    REN_JIAO_A("人教A"),
    ;

    @EnumValue
    @Schema(description = "出版社名称", implementation = String.class, example = "北师大")
    private final String value;


    PublisherType(String value) {
        this.value = value;
    }
}
