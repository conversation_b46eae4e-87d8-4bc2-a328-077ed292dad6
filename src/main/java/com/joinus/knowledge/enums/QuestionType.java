package com.joinus.knowledge.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum QuestionType {

    SINGLE_CHOICE("单选题", 1),
    MULTIPLE_CHOICE("选择题", 2),
    FILL_IN_THE_BLANK("填空题", 3),
    CALCULATION("计算题", 4),
    PROOF("证明题",5),
    APPLICATION("应用题", 6),
    PROBLEM_SOLVING("解答题", 7),
    TRUE_FALSE("判断题", 8),
    COMPOSITE("混合题", 9),
    MULTIPLE_ANSWER("多选题", 10),
    OTHER("其他", 99),
    UNKNOWN("未知类型", 100);

    @EnumValue
    private final String type;
    private Integer sortNo;



    public static List<Map<String, String>> list() {
        return Arrays.stream(QuestionType.values())
                .map(questionType -> Map.of(
                        "key", questionType.name(),
                        "value", questionType.getType()
                ))
                .collect(Collectors.toList());
    }

    public static QuestionType getByType(String type) {
        return Arrays.stream(QuestionType.values()).filter(questionType -> questionType.getType().equals(type)).findFirst().orElse(UNKNOWN);
    }
}
