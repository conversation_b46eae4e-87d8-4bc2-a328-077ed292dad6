package com.joinus.knowledge.config;

import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.mapper.MathKnowledgePointsMapper;
import com.joinus.knowledge.model.vo.MathKeyPointVO;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@Profile({"PRO","UAT"})
public class ProInitializer {

    @Autowired
    RedisTemplate<String, Object> redisTemplate;
    @Autowired
    MathKnowledgePointsMapper mathKnowledgePointsMapper;

    @PostConstruct
    private void init() {
        log.info("开始初始化小学知识点...");
        redisTemplate.delete(GlobalConstants.PRIMARY_KEY_POINTS_KEY);
        List<MathKeyPointVO> keyPoints = mathKnowledgePointsMapper.getPrimarySchoolKeyPoints();
        List<MathKeyPointVO> middleSchoolKnowledgePoints = mathKnowledgePointsMapper.listMiddleSchoolKnowledgePoints(PublisherType.REN_JIAO);
        List<MathKeyPointVO> highSchoolKnowledgePoints = mathKnowledgePointsMapper.listHighSchoolKnowledgePoints(PublisherType.REN_JIAO_A);
        List<PublisherType> publisherTypes = keyPoints.stream().map(MathKeyPointVO::getPublisher).distinct().toList();
        publisherTypes.forEach(publisherType -> {
            redisTemplate.delete(publisherType.name() + GlobalConstants.PUBLISHER_KEY_POINTS_KEY_SUFFIX);
            keyPoints.stream().filter(kp -> kp.getPublisher() == publisherType).forEach(kp -> {
                redisTemplate.opsForZSet().add(publisherType.name() + GlobalConstants.PUBLISHER_KEY_POINTS_KEY_SUFFIX, kp, kp.getSortNo());
                redisTemplate.opsForHash().put(GlobalConstants.PRIMARY_KEY_POINTS_KEY, kp.getKeyPointId().toString(), kp);
            });
            middleSchoolKnowledgePoints.forEach(kp ->
                    redisTemplate.opsForZSet().add(publisherType.name() + GlobalConstants.PUBLISHER_KEY_POINTS_KEY_SUFFIX, kp, 8888d)
            );
            highSchoolKnowledgePoints.forEach(kp ->
                    redisTemplate.opsForZSet().add(publisherType.name() + GlobalConstants.PUBLISHER_KEY_POINTS_KEY_SUFFIX, kp, 9999d)
            );
        });
    }

}
