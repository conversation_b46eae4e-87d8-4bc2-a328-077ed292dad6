package com.joinus.knowledge.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;

@Configuration
public class WebClientConfig {

    @Value("${smart-study.webclient.base-url:http://localhost:8081}")
    private String smartStudyBaseUrl;

    @Value("${spring.ai.deepseek-r1.base-url}")
    private String deepSeekR1BaseUrl;
    @Value("${spring.ai.deepseek-r1.api-key}")
    private String deepSeekR1ApiKey;

    @Value("${spring.ai.openai.base-url}")
    private String baseUrl;
    @Value("${spring.ai.openai.api-key}")
    private String apiKey;

    @Value("${spring.ai.gemini.base-url:https://gemini.dc6.memorysheep.com/v1beta/openai}")
    private String geminiBaseUrl;
    @Value("${spring.ai.gemini.api-key:AIzaSyBAtULRtGccIsaPBc02Hj2_QGHzvG5AAHU}")
    private String geminiApiKey;

    @Value("${spring.ai.qwen3.base-url:https://jysd-qwen3-235b.ijx.icu/v1}")
    private String qwen3BaseUrl;
    @Value("${spring.ai.qwen3.api-key:sk-jysd-279077d684634b0ba82e02193058533a}")
    private String qwen3ApiKey;

    @Value("${dify.url:https://dify.ijx.icu/v1}")
    private String difyUrl;

    @Value("${keycloak.url:https://keycloak.uat.967111.top}")
    private String keycloakUrl;

    @Value("${pdf.generate.service.url:https://pdf-generator-server.static.ijx.ink}")
    private String pdfGenerateServiceUrl;

    @Bean
    public WebClient webClient(WebClient.Builder builder) {
        return builder
                .baseUrl(smartStudyBaseUrl) // 根据需要设置基础 URL
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();
    }

    @Bean
    public WebClient jysdDeepseekR1WebClient(WebClient.Builder builder) {
        return builder
                .baseUrl(deepSeekR1BaseUrl) // 根据需要设置基础 URL
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + deepSeekR1ApiKey)
                .build();
    }

    @Bean
    public WebClient qwenVlWebClient(WebClient.Builder builder) {
        return builder
                .baseUrl(baseUrl) // 根据需要设置基础 URL
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
                .build();
    }

    @Bean
    public WebClient geminiWebClient(WebClient.Builder builder) {
        // 配置更长的超时
        HttpClient httpClient = HttpClient.create()
                .responseTimeout(Duration.ofSeconds(600));
        return builder
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .baseUrl(geminiBaseUrl) // 根据需要设置基础 URL
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + geminiApiKey)
                .build();
    }

    @Bean
    public WebClient qwen3WebClient(WebClient.Builder builder) {
        return builder
                .baseUrl(qwen3BaseUrl) // 根据需要设置基础 URL
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + qwen3ApiKey)
                .build();
    }

    @Bean
    public WebClient difyWebClient(WebClient.Builder builder) {
        // 配置更长的超时
        HttpClient httpClient = HttpClient.create()
                .responseTimeout(Duration.ofSeconds(1200));

        ExchangeStrategies exchangeStrategies = ExchangeStrategies.builder()
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(16 * 1024 *1024))
                .build();

        return builder
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .baseUrl(difyUrl)
                .exchangeStrategies(exchangeStrategies)
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();
    }

    @Bean
    public WebClient keycloakWebClient(WebClient.Builder builder) {
        return builder
                .baseUrl(keycloakUrl)
                .build();
    }

    @Bean
    public WebClient pdfGenerateServiceWebClient(WebClient.Builder builder) {
        return builder
                .baseUrl(pdfGenerateServiceUrl)
                .build();
    }
}
