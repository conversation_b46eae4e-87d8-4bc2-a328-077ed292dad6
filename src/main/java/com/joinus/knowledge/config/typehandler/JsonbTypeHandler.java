package com.joinus.knowledge.config.typehandler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * PostgreSQL JSONB类型处理器
 * 用于处理Java String类型与PostgreSQL JSONB类型的转换
 * 该处理器仅用于UserLog.content字段，不应该用于其他实体的普通字符串字段
 */
@MappedTypes(String.class)
public class JsonbTypeHandler extends BaseTypeHandler<String> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        // 检查参数是否为有效的JSON
        if (isValidJson(parameter)) {
            PGobject jsonObject = new PGobject();
            jsonObject.setType("jsonb");
            jsonObject.setValue(parameter);
            ps.setObject(i, jsonObject);
        } else {
            // 如果不是有效的JSON，则按普通字符串处理
            ps.setString(i, parameter);
        }
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Object obj = rs.getObject(columnName);
        return obj == null ? null : obj.toString();
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Object obj = rs.getObject(columnIndex);
        return obj == null ? null : obj.toString();
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Object obj = cs.getObject(columnIndex);
        return obj == null ? null : obj.toString();
    }
    
    /**
 * 判断字符串是否为有效的JSON
 * 使用更严格的JSON验证，避免误判数学表达式为JSON
 */
 private boolean isValidJson(String str) {
    if (str == null || str.isEmpty()) {
        return false;
    }
    
    str = str.trim();
    
    // 基本格式检查
    if (!((str.startsWith("{") && str.endsWith("}")) || 
          (str.startsWith("[") && str.endsWith("]")))){
        return false;
    }
    
    // 使用JSON解析器进行严格验证
    try {
        cn.hutool.json.JSONUtil.parse(str);
        return true;
    } catch (Exception e) {
        return false;
    }
 }
}
