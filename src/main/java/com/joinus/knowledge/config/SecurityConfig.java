package com.joinus.knowledge.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;

/**
 * Spring Security 配置类
 * 专门用于保护 Knife4j 接口文档页面
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Value("${knife4j.basic-auth.username:edu}")
    private String username;

    @Value("${knife4j.basic-auth.password:manage}")
    private String password;

    /**
     * 配置安全过滤器链
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // 禁用 CSRF，因为这是API文档页面
            .csrf(AbstractHttpConfigurer::disable)
            // 配置授权规则
            .authorizeHttpRequests(authz -> authz
                // Knife4j 相关路径需要认证
                .requestMatchers("/doc.html/**").authenticated()
                .requestMatchers("/webjars/**").authenticated()
                .requestMatchers("/swagger-ui/**").authenticated()
                .requestMatchers("/v3/api-docs/**").authenticated()
                .requestMatchers("/swagger-resources/**").authenticated()
                .requestMatchers("/favicon.ico").permitAll()
                // 其他所有请求都允许访问
                .anyRequest().permitAll()
            )
            // 配置 HTTP Basic 认证
            .httpBasic(httpBasic -> httpBasic
                .realmName("Knife4j API Documentation")
            );

        return http.build();
    }

    /**
     * 配置密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 配置用户详情服务
     */
    @Bean
    public UserDetailsService userDetailsService() {
        UserDetails user = User.builder()
                .username(username)
                .password(passwordEncoder().encode(password))
                .roles("ADMIN")
                .build();

        return new InMemoryUserDetailsManager(user);
    }
}
