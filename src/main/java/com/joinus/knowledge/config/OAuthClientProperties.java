package com.joinus.knowledge.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * OAuth 客户端配置属性类
 * 支持多个客户端配置管理
 */
@Data
@Component
@ConfigurationProperties(prefix = "keycloak.oauth")
public class OAuthClientProperties {

    /**
     * 多个客户端配置映射
     * key: client-id
     * value: 客户端配置信息
     */
    private Map<String, ClientConfig> clients;

    /**
     * 客户端配置信息
     */
    @Data
    public static class ClientConfig {
        /**
         * 客户端密钥
         */
        private String clientSecret;
        
        /**
         * 重定向URL
         */
        private String redirectUrl;
    }

    /**
     * 根据客户端ID获取配置
     * @param clientId 客户端ID
     * @return 客户端配置，如果不存在则返回null
     */
    public ClientConfig getClientConfig(String clientId) {
        return clients != null ? clients.get(clientId) : null;
    }

    /**
     * 检查客户端ID是否存在
     * @param clientId 客户端ID
     * @return 是否存在
     */
    public boolean hasClient(String clientId) {
        return clients != null && clients.containsKey(clientId);
    }
}
