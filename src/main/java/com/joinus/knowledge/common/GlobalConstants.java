package com.joinus.knowledge.common;

import java.util.List;

public class GlobalConstants {
    public final static String EXTRACT_INFO_FROM_IMAGE_BY_LLM_TASK_QUEUE= "extract-info-from-image-by-llm-workflow-task-queue";
    public final static String IMAGE_OCR_TASK_QUEUE= "image-ocr-workflow-task-queue";
    public final static String GENERATE_QUESTION_KNOWLEDGE_DOMAIN_TASK_QUEUE= "generate-question-knowledge-domain-workflow-task-queue";
    public final static String ANALYZE_EXAM_KNOWLEDGE_POINTS_TASK_QUEUE= "analyze-exam-knowledge-poinits-workflow-task-queue";
    public final static String MATH_EXAM_REANALYZE_KNOWLWDGE_POINT_TASK_QUEUE = "math-exam-reanalyze-knowledge-point-workflow-task-queue";
    public final static String MATH_KNOWLEDGE_POINT_HANDOUT_PDF_GENERATE_TASK_QUEUE = "math-knowledge-point-handout-pdf-generate-task-queue";

    public final static String MINIO_BUCKET_NAME = "education-knowledge-hub";

    public static List<String> QUESTION_ERROR_LABEL_TYPES = List.of("CORRECTNESS", "LATEX_SYNTAX", "ILLUSTRATION_NEED");
    public static List<String> QUESTION_COMMON_LABEL_TYPES = List.of("KNOWLEDGE_DOMAIN");

    public final static String PRIMARY_KEY_POINTS_KEY = "PRIMARY:KEY_POINTS";
    public final static String PUBLISHER_KEY_POINTS_KEY_SUFFIX = "_KEY_POINTS";

    public interface REVIEW_STATUS{
        String APPROVED = "APPROVED";
        String REJECTED = "REJECTED";
        String PENDING = "PENDING";
    }

}
