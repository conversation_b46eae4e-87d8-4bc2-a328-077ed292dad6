package com.joinus.knowledge.temporal.activity.impl;

import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.model.entity.MathKnowledgePointHandout;
import com.joinus.knowledge.model.param.UploadFileParam;
import com.joinus.knowledge.service.MathKnowledgePointHandoutFileService;
import com.joinus.knowledge.service.MathKnowledgePointHandoutService;
import com.joinus.knowledge.service.impl.PdfGenerator;
import com.joinus.knowledge.temporal.activity.HandoutActivity;
import io.temporal.spring.boot.ActivityImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

@Component
@ActivityImpl(taskQueues = {GlobalConstants.MATH_KNOWLEDGE_POINT_HANDOUT_PDF_GENERATE_TASK_QUEUE})
public class HandoutActivityImpl implements HandoutActivity {

    @Resource
    private MathKnowledgePointHandoutService handoutService;
    @Resource
    private PdfGenerator pdfGenerator;
    @Resource
    private MathKnowledgePointHandoutFileService handoutFileService;


    @Override
    public List<MathKnowledgePointHandout> queryReviewedWithoutHtmlHandouts(int limit) {
        return handoutService.queryReviewedWithoutHtmlHandouts(limit);
    }

    @Override
    public String generateHandoutPdf(String contentMarkdown, UUID knowledgePointId) {
        String fileName = handoutFileService.generateHandoutFileName(knowledgePointId);
        return pdfGenerator.generateHandoutPdf(contentMarkdown, fileName);
    }

    @Override
    public String generateHandoutHtml(String contentMarkdown, UUID knowledgePointId) {
        String fileName = handoutFileService.generateHandoutFileName(knowledgePointId);
        return pdfGenerator.generateHandoutHtml(contentMarkdown, fileName);
    }

    @Override
    public void saveKnowledgePointHandoutFileAndRelation(UUID id, List<UploadFileParam> uploadFiles) {
        handoutFileService.saveKnowledgePointHandoutFileAndRelation(id, uploadFiles);
    }
}
