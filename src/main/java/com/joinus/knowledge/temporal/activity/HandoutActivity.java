package com.joinus.knowledge.temporal.activity;

import com.joinus.knowledge.model.entity.MathKnowledgePointHandout;
import com.joinus.knowledge.model.param.UploadFileParam;
import io.temporal.activity.ActivityInterface;
import io.temporal.activity.ActivityMethod;

import java.util.List;
import java.util.UUID;

@ActivityInterface
public interface HandoutActivity {

    @ActivityMethod
    List<MathKnowledgePointHandout> queryReviewedWithoutHtmlHandouts(int limit);

    @ActivityMethod
    String generateHandoutPdf(String contentMarkdown, UUID knowledgePointId);

    @ActivityMethod
    String generateHandoutHtml(String contentMarkdown, UUID knowledgePointId);

    @ActivityMethod
    void saveKnowledgePointHandoutFileAndRelation(UUID id, List<UploadFileParam> uploadFiles);

}
