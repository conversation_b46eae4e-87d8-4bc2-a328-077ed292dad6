package com.joinus.knowledge.temporal.workflow.impl;

import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.model.entity.MathKnowledgePointHandout;
import com.joinus.knowledge.model.param.UploadFileParam;
import com.joinus.knowledge.temporal.activity.HandoutActivity;
import com.joinus.knowledge.temporal.workflow.GenerateHandoutPdfWorkflow;
import io.temporal.activity.ActivityOptions;
import io.temporal.common.RetryOptions;
import io.temporal.spring.boot.WorkflowImpl;
import io.temporal.workflow.Workflow;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;

@Component
@WorkflowImpl(taskQueues = GlobalConstants.MATH_KNOWLEDGE_POINT_HANDOUT_PDF_GENERATE_TASK_QUEUE)
public class GenerateHandoutPdfWorkflowImpl implements GenerateHandoutPdfWorkflow {

    ActivityOptions options = ActivityOptions.newBuilder()
            .setStartToCloseTimeout(Duration.ofMinutes(10))
            .setHeartbeatTimeout(Duration.ofMinutes(10))
            .setRetryOptions(RetryOptions.newBuilder()
                    .setMaximumAttempts(3)
                    .build())
            .build();

    @Override
    public void execute() {
        HandoutActivity handoutActivity = Workflow.newActivityStub(HandoutActivity.class, options);
        List<MathKnowledgePointHandout> handoutList = handoutActivity.queryReviewedWithoutHtmlHandouts(100);
        for (MathKnowledgePointHandout handout : handoutList) {
            String pdfKey = handoutActivity.generateHandoutPdf(handout.getContentMarkdown(), handout.getKnowledgePointId());
            String htmlKey = handoutActivity.generateHandoutHtml(handout.getContentMarkdown(), handout.getKnowledgePointId());
            UploadFileParam pdfFileParam = new UploadFileParam();
            pdfFileParam.setOssEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB);
            pdfFileParam.setOssKey(pdfKey);
            UploadFileParam htmlFileParam = new UploadFileParam();
            htmlFileParam.setOssEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB);
            htmlFileParam.setOssKey(htmlKey);
            handoutActivity.saveKnowledgePointHandoutFileAndRelation(handout.getId(), List.of(pdfFileParam, htmlFileParam));
        }
    }
}
