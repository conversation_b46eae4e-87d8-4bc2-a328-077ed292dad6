package com.joinus.knowledge.temporal.workflow.impl;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.enums.ExamStateEnum;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathExam;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.entity.QuestionFile;
import com.joinus.knowledge.model.param.AnalyzeExamParam;
import com.joinus.knowledge.model.vo.KnowledgePointsAndDifficultyVO;
import com.joinus.knowledge.temporal.activity.AnalyzeExamKnowledgePointsActivity;
import com.joinus.knowledge.temporal.activity.MathExamActivity;
import com.joinus.knowledge.temporal.workflow.AnalyzeExamKnowledgePointsWorkflow;
import io.temporal.activity.ActivityOptions;
import io.temporal.common.RetryOptions;
import io.temporal.spring.boot.WorkflowImpl;
import io.temporal.workflow.Async;
import io.temporal.workflow.Promise;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Component
@WorkflowImpl(taskQueues = GlobalConstants.ANALYZE_EXAM_KNOWLEDGE_POINTS_TASK_QUEUE)
public class AnalyzeExamKnowledgePointsWorkflowImpl implements AnalyzeExamKnowledgePointsWorkflow {

    private final ActivityOptions options = ActivityOptions.newBuilder()
            .setStartToCloseTimeout(Duration.ofMinutes(10))
            .setRetryOptions(RetryOptions.newBuilder()
                    .setMaximumAttempts(4)
                    .build())
            .build();

    /**
     * 分析试卷知识点
     * pc端试卷纠错后，解题并分析题目知识点信息
     * @param param
     */
    @Override
    public void submit(AnalyzeExamParam param) {
        // 创建活动存根
        AnalyzeExamKnowledgePointsActivity analyzeActivity = Workflow.newActivityStub(AnalyzeExamKnowledgePointsActivity.class, options);
        MathExamActivity mathExamActivity = Workflow.newActivityStub(MathExamActivity.class, options);

        try {
            Workflow.getLogger(this.getClass()).info("开始分析试卷知识点: param={}", JSONUtil.toJsonStr(param));

            String logPrefix = "AnalyzeExamKnowledgePointsWorkflow " + param.getExamId();

            MathExam mathExam = mathExamActivity.getExamById(param.getExamId());

            if (null == mathExam) {
                Workflow.getLogger(this.getClass()).warn("试卷不存在: examId={}", param.getExamId());
                log.info(logPrefix + "试卷不存在");
                return;
            }

            if (null == param.getPublisher()) {
                Workflow.getLogger(this.getClass()).warn("教材版本不能为空", param.getPublisher());
                log.info(logPrefix + "教材版本不能为空 {}", param.getPublisher());
                return;
            }

            // 根据试卷id查询题目列表 - 使用CutMathExamActivity的getExamQuestions方法
            List<MathQuestion> questions = mathExamActivity.getExamQuestions(param.getExamId());
            Workflow.getLogger(this.getClass()).info("获取到题目数量: {}", questions.size());

            if (questions.isEmpty()) {
                Workflow.getLogger(this.getClass()).warn("试卷中没有题目: examId={}", param.getExamId());
                mathExamActivity.updateExamState(param.getExamId(), ExamStateEnum.DONE);
                analyzeActivity.callbackForManualAnalyze(param, logPrefix);
                log.info(logPrefix + "试卷中没有题目");
                return;
            }
            log.info(logPrefix + "获取到题目 size {} {}", questions.size(), JSONUtil.toJsonStr( questions));
            
            List<QuestionFile> questionFiles = mathExamActivity.listQuestionFile(questions.stream().map(MathQuestion::getId).toList());

            Map<UUID, List<UUID>> questionFileList = questionFiles.stream()
                    .collect(Collectors.groupingBy(QuestionFile::getQuestionId, Collectors.mapping(QuestionFile::getFileId, Collectors.toList())));

            log.info(logPrefix + "获取到题目文件 {}", questionFiles.size());

            processQuestionsBatchAsync(param, questions, mathExam, questionFileList, analyzeActivity);

            log.info(logPrefix + "完成处理，处理数量: {}", questions.size());
            // 更新试卷状态 - 使用活动接口
            mathExamActivity.updateExamState(param.getExamId(), ExamStateEnum.DONE);
            log.info(logPrefix + "更新试卷状态为 DONE");
            // 执行回调
            String callbackResult = analyzeActivity.callbackForManualAnalyze(param, logPrefix);
            Workflow.getLogger(this.getClass()).info("回调执行结果: {}", callbackResult);
            
        } catch (Exception e) {
            Workflow.getLogger(this.getClass()).error("分析试卷知识点工作流执行异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    private Map<String, Object> solveQuestionAndRelateKnowledgePoints(MathQuestion question, MathExam mathExam, Map<UUID, List<UUID>> questionFileList, AnalyzeExamKnowledgePointsActivity analyzeActivity, PublisherType publisher) {
        HashMap<String, Object> result = new HashMap<>();
        analyzeActivity.solveQuestion(question, mathExam, questionFileList);
        KnowledgePointsAndDifficultyVO knowledgePointsAndDifficultyVO = analyzeActivity.analyzeQuestionKnowledgePoints(question, mathExam.getGrade(), mathExam.getSemester(), publisher);
        result.put("question", question);
        result.put("knowledgePointsAndDifficultyVO", knowledgePointsAndDifficultyVO);
        return result;
    }

    /**
     * 方案一：按批次异步处理
     */
    private void processQuestionsBatchAsync(AnalyzeExamParam param, List<MathQuestion> questions, MathExam mathExam, Map<UUID, List<UUID>> questionFileList, AnalyzeExamKnowledgePointsActivity analyzeActivity) {

        int maxConcurrency = 5; // 最大并发数

        List<List<MathQuestion>> questionBatches = Lists.partition(questions, maxConcurrency);

        for (List<MathQuestion> batch : questionBatches) {
            // 为当前批次创建异步任务
            List<Promise<Map<String, Object>>> promises = batch.stream()
                    .map(question -> Async.function(() -> {
                        try {
                            return solveQuestionAndRelateKnowledgePoints(question, mathExam, questionFileList, analyzeActivity, param.getPublisher());
                        } catch (Exception e) {
                            Workflow.getLogger(this.getClass()).error("处理题目失败: questionId={}, 错误={}",
                                    question.getId(), e.getMessage());
                            // 返回空结果，不中断整个流程
                            return null;
                        }
                    }))
                    .toList();

            // 等待当前批次的所有任务完成
            Promise.allOf(promises).get();

            Workflow.getLogger(this.getClass()).info("完成批次处理，批次大小: {}", batch.size());
        }
    }

    /**
     * 方案二：全异步处理（适用于题目数量不太多的情况）
     */
    private void processQuestionsFullAsync(List<MathQuestion> questions, MathExam mathExam,
                                         Map<UUID, List<UUID>> questionFileList,
                                         AnalyzeExamKnowledgePointsActivity analyzeActivity, PublisherType publisher) {
        // 创建所有异步任务
        List<Promise<Map<String, Object>>> allPromises = questions.stream()
            .map(question -> Async.function(() -> {
                try {
                    return solveQuestionAndRelateKnowledgePoints(question, mathExam, questionFileList, analyzeActivity, publisher);
                } catch (Exception e) {
                    Workflow.getLogger(this.getClass()).error("处理题目失败: questionId={}, 错误={}",
                            question.getId(), e.getMessage());
                    return null;
                }
            }))
            .toList();

        // 等待所有任务完成
        Promise.allOf(allPromises).get();

        Workflow.getLogger(this.getClass()).info("所有题目处理完成，总数: {}", questions.size());
    }

    /**
     * 方案三：动态并发控制（类似线程池的效果）
     */
    private void processQuestionsWithDynamicConcurrency(List<MathQuestion> questions, MathExam mathExam,
                                                        Map<UUID, List<UUID>> questionFileList,
                                                        AnalyzeExamKnowledgePointsActivity analyzeActivity,
                                                        PublisherType publisher) {
        int maxConcurrency = 8;
        int totalQuestions = questions.size();
        int processedCount = 0;

        while (processedCount < totalQuestions) {
            // 获取下一批要处理的题目
            int endIndex = Math.min(processedCount + maxConcurrency, totalQuestions);
            List<MathQuestion> currentBatch = questions.subList(processedCount, endIndex);

            // 创建当前批次的异步任务
            List<Promise<Map<String, Object>>> batchPromises = currentBatch.stream()
                .map(question -> Async.function(() -> {
                    try {
                        Workflow.getLogger(this.getClass()).info("开始处理题目: questionId={}", question.getId());
                        Map<String, Object> result = solveQuestionAndRelateKnowledgePoints(
                            question, mathExam, questionFileList, analyzeActivity, publisher);
                        Workflow.getLogger(this.getClass()).info("完成处理题目: questionId={}", question.getId());
                        return result;
                    } catch (Exception e) {
                        Workflow.getLogger(this.getClass()).error("处理题目失败: questionId={}, 错误={}",
                                question.getId(), e.getMessage());
                        return null;
                    }
                }))
                .toList();

            // 等待当前批次完成
            Promise.allOf(batchPromises).get();

            processedCount = endIndex;
            Workflow.getLogger(this.getClass()).info("批次处理进度: {}/{}", processedCount, totalQuestions);
        }
    }
}
