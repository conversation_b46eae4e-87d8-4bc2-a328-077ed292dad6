package com.joinus.knowledge.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.model.param.EnableOrDisableEnglishAiQuestionParam;
import com.joinus.knowledge.model.param.EnglishAiQuestionPageQueryParam;
import com.joinus.knowledge.model.vo.EnglishAiQuestionDetailVO;
import com.joinus.knowledge.model.vo.EnglishAiQuestionVO;
import com.joinus.knowledge.service.EnglishAiQuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "英语题库管理接口", description = "提供英语题库管理接口相关功能")
@RequestMapping("/english/ai/question")
public class EnglishAiQuestionController {

    private final EnglishAiQuestionService englishAiQuestionService;

    /**
     * 题目分页查询接口
     * GET /api/math/exams/page
     * 支持条件：试卷名称、试卷来源、年级、学期、年份、所属学校、试卷类型
     */
    @Operation(summary = "分页")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "分页查询成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnglishAiQuestionVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/page")
    public Result<IPage<EnglishAiQuestionVO>> pageQuery(@ParameterObject EnglishAiQuestionPageQueryParam param) {
        IPage<EnglishAiQuestionVO> result = englishAiQuestionService.pageQuery(param);
        return Result.success(result);
    }


    @Operation(summary = "详情")
    @Parameters({
            @Parameter(name = "id", description = "题目ID", required = true, example = "8653cbe2-fc68-406c-8bf6-ae7e85e887aa")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "题目详情查询成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnglishAiQuestionDetailVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/{id}")
    public Result<EnglishAiQuestionDetailVO> queryExamDetail(@PathVariable("id") UUID id) {
        EnglishAiQuestionDetailVO vo = englishAiQuestionService.queryDetailById(id);
        return Result.success(vo);
    }

    @Operation(summary = "批量启用")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "批量启用成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PutMapping("/batch-enable")
    public Result<String> batchEnable(@Validated @RequestBody EnableOrDisableEnglishAiQuestionParam param) {
        englishAiQuestionService.batchEnable(param);
        return Result.success();
    }

    @Operation(summary = "批量禁用")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "批量禁用成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PutMapping("/batch-disable")
    public Result<String> batchDisable(@Validated @RequestBody EnableOrDisableEnglishAiQuestionParam param) {
        englishAiQuestionService.batchDisable(param);
        return Result.success();
    }

}
