package com.joinus.knowledge.controller;

import com.aliyun.sts20150401.models.AssumeRoleResponseBody;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.config.AliOssProperties;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.model.vo.OssAliTokenVO;
import com.joinus.knowledge.model.vo.OssFileVO;
import com.joinus.knowledge.service.OssService;
import com.joinus.knowledge.utils.AliOssUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/oss")
public class OssController {

    @Resource
    private OssService ossService;
    @Resource
    private AliOssUtils aliOssUtils;
    @Resource
    private AliOssProperties aliOssProperties;

    @GetMapping("/presigned-url")
    public Result<OssFileVO> getPresignedUrl(@RequestParam("ossEnum") OssEnum ossEnum,
                                             @RequestParam("ossKey") String ossKey) {
        OssFileVO ossFileVO = ossService.getPresignedInfo(ossEnum, ossKey);
        return Result.success(ossFileVO);
    }

    @Operation(summary = "获取ali OSS临时凭证", description = "获取ali OSS临时凭证")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功解析题目坐标", content = @Content(mediaType = "application/json", schema = @Schema(implementation = OssAliTokenVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/ali/token")
    public Result<OssAliTokenVO> getOssToken() throws Exception {
        AssumeRoleResponseBody.AssumeRoleResponseBodyCredentials credentials = aliOssUtils.getToken();
        OssAliTokenVO ossAliTokenVO = OssAliTokenVO.builder()
                .bucket(aliOssProperties.getBucketName())
                .region(aliOssProperties.getRegion())
                //这个endpoint已解决pc端跨域问题，公司oss域名的话会出现跨域问题，app端没有这个现象。
                .endpoint("https://oss-cn-beijing.aliyuncs.com")
                .accessKeyId(credentials.getAccessKeyId())
                .accessKeySecret(credentials.getAccessKeySecret())
                .expiration(credentials.getExpiration())
                .securityToken(credentials.getSecurityToken())
                .build();
        return Result.success(ossAliTokenVO);
    }
}
