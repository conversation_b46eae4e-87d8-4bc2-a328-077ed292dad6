package com.joinus.knowledge.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.model.param.*;
import com.joinus.knowledge.model.vo.*;
import com.joinus.knowledge.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@Slf4j
@RestController
@AllArgsConstructor
@Tag(name = "英语试卷管理接口", description = "提供英语试卷管理接口相关功能")
@RequestMapping("/english/exam")
public class EnglishExamController {

    private final EnglishExamService englishExamService;

    /**
     * 试卷分页查询接口
     * GET /api/math/exams/page
     * 支持条件：试卷名称、试卷来源、年级、学期、年份、所属学校、试卷类型
     */
    @Operation(summary = "分页")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "分页查询成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = MathExamVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/page")
    public Result<IPage<EnglishExamVO>> pageQuery(@ParameterObject EnglishExamPageQueryParam param) {
        IPage<EnglishExamVO> result = englishExamService.pageQuery(param);
        return Result.success(result);
    }


    @Operation(summary = "试卷详情")
    @Parameters({
            @Parameter(name = "id", description = "试卷ID", required = true, example = "8653cbe2-fc68-406c-8bf6-ae7e85e887aa")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "试卷详情查询成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = EnglishExamVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/{id}")
    public Result<EnglishExamVO> queryExamDetail(@PathVariable("id") UUID id) {
        EnglishExamVO englishExamVO = englishExamService.queryExamDetailById(id);
        return Result.success(englishExamVO);
    }

    @Operation(summary = "编辑试卷")
    @Parameters({
            @Parameter(name = "id", description = "试卷ID", required = true, example = "8653cbe2-fc68-406c-8bf6-ae7e85e887aa")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "编辑试卷成功",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = FileVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PutMapping("/{id}")
    public Result<String> updateExamDetail(@PathVariable("id") UUID id,
                                           @RequestBody UpdateEnglishExamParam param) {
        englishExamService.updateExamDetail(id, param);
        return Result.success();
    }

    @Operation(summary = "学校下拉列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "年级下拉列表查询成功"),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/school/list")
    public Result<List<String>> listSchool(@RequestParam(value = "schoolName", required = false) String schoolName) {
        List<String> voList = englishExamService.listSchool(schoolName);
        return Result.success(voList);
    }
}
