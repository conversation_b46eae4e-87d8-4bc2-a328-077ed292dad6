package com.joinus.knowledge.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.config.base.BusinessException;
import com.joinus.knowledge.model.entity.MathCatalogNodes;
import com.joinus.knowledge.model.param.ChapterParam;
import com.joinus.knowledge.model.param.SectionParam;
import com.joinus.knowledge.service.MathCatalogNodesService;
import com.joinus.knowledge.service.TextbooksService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 数学章节管理 Controller
 */
@RestController
@RequestMapping("/math-chapters")
@RequiredArgsConstructor
public class MathChapterController {

    @Autowired
    private TextbooksService textbooksService;
    @Autowired
    private MathCatalogNodesService mathCatalogNodesService;

    /**
     * 为指定教材批量添加章节和小节
     * POST /api/math-chapters/textbook/{textbookId}
     */
    @PostMapping("/textbook/{textbookId}")
    @Transactional(rollbackFor = Exception.class)
    public Result<List<Map<String, Object>>> addChaptersWithSections(
            @PathVariable("textbookId") UUID textbookId,
            @RequestBody List<ChapterParam> chaptersParams) {
        
        // 检查教材是否存在
        if (textbooksService.getById(textbookId) == null) {
            return Result.error("教材不存在");
        }
        
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (ChapterParam chapterParam : chaptersParams) {
            // 验证必填字段
            if (chapterParam.getChapterName() == null || chapterParam.getChapterName().trim().isEmpty()) {
                return Result.error("章节名称不能为空");
            }
            if (chapterParam.getSortNo() == null) {
                return Result.error("章节排序号不能为空");
            }
            
            // 转换为实体对象
            MathCatalogNodes chapter = new MathCatalogNodes();
            chapter.setName(chapterParam.getChapterName());
            chapter.setSortNo(chapterParam.getSortNo());
            chapter.setTextbookId(textbookId);
            chapter.setLevel(1);
            
            // 保存章节
            boolean chapterSaved = mathCatalogNodesService.save(chapter);
            if (!chapterSaved) {
                return Result.error("章节保存失败");
            }
            
            // 处理章节的小节
            List<Map<String, Object>> sectionsList = new ArrayList<>();
            List<SectionParam> sectionsParams = chapterParam.getSections();
            
            if (sectionsParams != null && !sectionsParams.isEmpty()) {
                for (SectionParam sectionParam : sectionsParams) {
                    // 验证小节必填字段
                    if (sectionParam.getSectionName() == null || sectionParam.getSectionName().trim().isEmpty()) {
                        return Result.error("小节名称不能为空");
                    }
                    if (sectionParam.getSortNo() == null) {
                        return Result.error("小节排序号不能为空");
                    }
                    
                    // 转换为实体对象
                    MathCatalogNodes section = new MathCatalogNodes();
                    section.setName(sectionParam.getSectionName());
                    section.setSortNo(sectionParam.getSortNo());
                    section.setStartPage(sectionParam.getStartPage());
                    section.setEndPage(sectionParam.getEndPage());
                    section.setParentId(chapter.getId());
                    section.setLevel(2);
                    
                    // 保存小节
                    boolean sectionSaved = mathCatalogNodesService.save(section);
                    if (!sectionSaved) {
                        return Result.error("小节保存失败");
                    }
                    
                    // 构建小节返回数据
                    Map<String, Object> sectionMap = new HashMap<>();
                    sectionMap.put("id", section.getId());
                    sectionMap.put("sectionName", section.getName());
                    sectionMap.put("sortNo", section.getSortNo());
                    sectionMap.put("startPage", section.getStartPage());
                    sectionMap.put("endPage", section.getEndPage());
                    
                    sectionsList.add(sectionMap);
                }
            }
            
            // 构建章节返回数据
            Map<String, Object> chapterMap = new HashMap<>();
            chapterMap.put("id", chapter.getId());
            chapterMap.put("chapterName", chapter.getName());
            chapterMap.put("sortNo", chapter.getSortNo());
            chapterMap.put("sections", sectionsList);
            
            result.add(chapterMap);
        }
        
        return Result.success(result);
    }
    
    /**
     * 获取教材的所有章节及其小节
     * GET /api/math-chapters/textbook/{textbookId}
     */
    @GetMapping("/textbook/{textbookId}")
    public Result<List<Map<String, Object>>> getChaptersByTextbookId(@PathVariable("textbookId") UUID textbookId) {
        // 检查教材是否存在
        if (textbooksService.getById(textbookId) == null) {
            throw new BusinessException("教材不存在");
        }
        
        // 查询所有章节（level=1且textbook_id不为空）
        LambdaQueryWrapper<MathCatalogNodes> chapterWrapper = new LambdaQueryWrapper<>();
        chapterWrapper.eq(MathCatalogNodes::getTextbookId, textbookId);
        chapterWrapper.eq(MathCatalogNodes::getLevel, 1);
        chapterWrapper.orderByAsc(MathCatalogNodes::getSortNo);
        chapterWrapper.isNull(MathCatalogNodes::getDeletedAt);

        List<MathCatalogNodes> chapters = mathCatalogNodesService.list(chapterWrapper);
        List<Map<String, Object>> result = new ArrayList<>();

        // 获取各章节的小节并转换为用户期望的JSON格式
        for (MathCatalogNodes chapter : chapters) {
            Map<String, Object> chapterMap = new HashMap<>();

            // 转换字段名：从name到chapterName
            chapterMap.put("id", chapter.getId());
            chapterMap.put("chapterName", chapter.getName());
            chapterMap.put("sortNo", chapter.getSortNo());
            chapterMap.put("textbookId", chapter.getTextbookId());

            // 查询并添加小节（parent_id为当前章节ID的叶节点）
            LambdaQueryWrapper<MathCatalogNodes> sectionWrapper = new LambdaQueryWrapper<>();
            sectionWrapper.eq(MathCatalogNodes::getParentId, chapter.getId());
            sectionWrapper.orderByAsc(MathCatalogNodes::getSortNo);
            sectionWrapper.isNull(MathCatalogNodes::getDeletedAt);
            sectionWrapper.isNotNull(MathCatalogNodes::getStartPage);
            sectionWrapper.isNotNull(MathCatalogNodes::getEndPage);

            List<MathCatalogNodes> sections = mathCatalogNodesService.list(sectionWrapper);
            List<Map<String, Object>> sectionsList = new ArrayList<>();

            for (MathCatalogNodes section : sections) {
                Map<String, Object> sectionMap = new HashMap<>();
                sectionMap.put("id", section.getId());
                sectionMap.put("sectionName", section.getName());
                sectionMap.put("sortNo", section.getSortNo());
                sectionMap.put("startPage", section.getStartPage());
                sectionMap.put("endPage", section.getEndPage());
                sectionMap.put("chapterId", section.getParentId());

                sectionsList.add(sectionMap);
            }

            chapterMap.put("sections", sectionsList);
            result.add(chapterMap);
        }
        
        return Result.success(result);
    }
    
    /**
     * 根据ID获取章节详情及其小节
     * GET /api/math-chapters/{id}
     */
    @GetMapping("/{id}")
    public Result<Map<String, Object>> getChapterById(@PathVariable("id") UUID id) {
        MathCatalogNodes chapter = mathCatalogNodesService.getById(id);
        if (chapter == null || chapter.getDeletedAt() != null) {
            return Result.error("章节不存在或已删除");
        }
        
        // 构建章节数据
        Map<String, Object> chapterMap = new HashMap<>();
        chapterMap.put("id", chapter.getId());
        chapterMap.put("chapterName", chapter.getName());
        chapterMap.put("sortNo", chapter.getSortNo());
        chapterMap.put("textbookId", chapter.getTextbookId());
        
        // 获取章节的小节
        LambdaQueryWrapper<MathCatalogNodes> sectionWrapper = new LambdaQueryWrapper<>();
        sectionWrapper.eq(MathCatalogNodes::getParentId, chapter.getId());
        sectionWrapper.orderByAsc(MathCatalogNodes::getSortNo);
        sectionWrapper.isNull(MathCatalogNodes::getDeletedAt);
        
        List<MathCatalogNodes> sections = mathCatalogNodesService.list(sectionWrapper);
        List<Map<String, Object>> sectionsList = new ArrayList<>();
        
        for (MathCatalogNodes section : sections) {
            Map<String, Object> sectionMap = new HashMap<>();
            sectionMap.put("id", section.getId());
            sectionMap.put("sectionName", section.getName());
            sectionMap.put("sortNo", section.getSortNo());
            sectionMap.put("startPage", section.getStartPage());
            sectionMap.put("endPage", section.getEndPage());
            
            sectionsList.add(sectionMap);
        }
        
        chapterMap.put("sections", sectionsList);
        
        return Result.success(chapterMap);
    }
    
    /**
     * 更新章节信息
     * PUT /api/math-chapters/{id}
     */
    @PutMapping("/{id}")
    public Result<Map<String, Object>> updateChapter(@PathVariable("id") UUID id, @RequestBody ChapterParam chapterParam) {
        MathCatalogNodes existingChapter = mathCatalogNodesService.getById(id);
        if (existingChapter == null) {
            return Result.error("章节不存在");
        }
        
        // 更新章节信息
        existingChapter.setName(chapterParam.getChapterName());
        existingChapter.setSortNo(chapterParam.getSortNo());
        
        boolean success = mathCatalogNodesService.updateById(existingChapter);
        if (!success) {
            return Result.error("更新章节失败");
        }
        
        // 构建返回数据
        Map<String, Object> result = new HashMap<>();
        result.put("id", existingChapter.getId());
        result.put("chapterName", existingChapter.getName());
        result.put("sortNo", existingChapter.getSortNo());
        result.put("textbookId", existingChapter.getTextbookId());
        
        return Result.success(result);
    }
    
    /**
     * 删除章节及其所有小节
     * DELETE /api/math-chapters/{id}
     */
    @DeleteMapping("/{id}")
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteChapter(@PathVariable("id") UUID id) {
        MathCatalogNodes chapter = mathCatalogNodesService.getById(id);
        if (chapter == null) {
            return Result.error("章节不存在");
        }

        // 查询此节点下所有节点，包含此节点
        LambdaQueryWrapper<MathCatalogNodes> sectionWrapper = new LambdaQueryWrapper<>();
        sectionWrapper.apply("id_path <@ {0}::ltree", chapter.getIdPath());
        
        // 逻辑删除所有小节
        boolean sectionsDeleted = mathCatalogNodesService.remove(sectionWrapper);
        if (!sectionsDeleted) {
            return Result.error("删除小节失败");
        }
        
        return Result.success();
    }
}
