package com.joinus.knowledge.controller;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.config.OAuthClientProperties;
import com.joinus.knowledge.model.dto.TokenData;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/math/oauth")
@RequiredArgsConstructor
public class MathOauthController {

    @Resource
    private WebClient keycloakWebClient;

    @Resource
    private OAuthClientProperties oAuthClientProperties;

    @Value("${keycloak.realm.math:math}")
    private String realm;

    @PostMapping("/login-by-code")
    public Result<Map> loginByCode(@RequestParam("code") String code,
                                   @RequestParam("clientId") String clientId) {
        // 获取客户端配置
        OAuthClientProperties.ClientConfig clientConfig = getClientConfig(clientId);
        String clientSecret = clientConfig.getClientSecret();
        String redirectUrl = clientConfig.getRedirectUrl();

        MultiValueMap<String, String> loginParam = new LinkedMultiValueMap<>();
        loginParam.put("client_id", List.of(clientId));
        loginParam.put("client_secret", List.of(clientSecret));
        loginParam.put("grant_type", List.of("authorization_code"));
        loginParam.put("code", List.of(code));
        loginParam.put("redirect_uri", List.of(redirectUrl));

        TokenData tokenData = keycloakWebClient.post()
                .uri("/realms/{realm}/protocol/openid-connect/token", realm)
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(BodyInserters.fromFormData(loginParam))
                .retrieve()
                .bodyToMono(TokenData.class)
                .timeout(Duration.ofMillis(6000))
                .block();

        MultiValueMap<String, String> userInfoParam = new LinkedMultiValueMap<>();
        userInfoParam.put("client_id", List.of(clientId));
        userInfoParam.put("client_secret", List.of(clientSecret));
        Assert.isTrue(tokenData != null, "登录失败");
        userInfoParam.put("token", List.of(tokenData.getAccessToken()));

        Map map = keycloakWebClient.post()
                .uri("/realms/{realm}/protocol/openid-connect/token/introspect", realm)
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(BodyInserters.fromFormData(userInfoParam))
                .retrieve()
                .bodyToMono(Map.class)
                .timeout(Duration.ofMillis(6000))
                .block();
        Map<String, String> result = new HashMap<>(4);
        result.put("access_token", tokenData.getAccessToken());
        result.put("refresh_token", tokenData.getRefreshToken());
        result.put("username", MapUtil.getStr(map, "username"));
        return Result.success(result);
    }

    @PostMapping("/logout")
    public Result<String> logout(@RequestParam("refreshToken") String refreshToken,
                                 @RequestParam("clientId") String clientId) {
        // 获取客户端配置
        OAuthClientProperties.ClientConfig clientConfig = getClientConfig(clientId);
        String clientSecret = clientConfig.getClientSecret();

        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.add("client_id", clientId);
        formData.add("client_secret", clientSecret);
        formData.add("refresh_token", refreshToken);

        String response = keycloakWebClient.post()
                .uri("/realms/{realm}/protocol/openid-connect/logout", realm)
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(BodyInserters.fromFormData(formData))
                .retrieve()
                .bodyToMono(String.class)
                .block();

        return Result.success(response);
    }

    /**
     * 获取客户端配置
     * 优先从新配置中获取，如果不存在则使用默认配置（向后兼容）
     *
     * @param clientId 客户端ID
     * @return 客户端配置
     * @throws IllegalArgumentException 如果客户端ID不存在且不是默认客户端
     */
    private OAuthClientProperties.ClientConfig getClientConfig(String clientId) {
        Assert.isTrue(StrUtil.isNotBlank(clientId), "客户端ID不能为空");

        if (oAuthClientProperties.hasClient(clientId)) {
            return oAuthClientProperties.getClientConfig(clientId);
        }

        throw new IllegalArgumentException("不支持的客户端ID: " + clientId);
    }
}
