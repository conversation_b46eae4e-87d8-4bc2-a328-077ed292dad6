package com.joinus.knowledge.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.EducationalStage;
import com.joinus.knowledge.enums.MathQuestionReviewType;
import com.joinus.knowledge.model.entity.MathLabel;
import com.joinus.knowledge.model.entity.MathQuestionRelationships;
import com.joinus.knowledge.model.entity.MathQuestionReviewRecords;
import com.joinus.knowledge.model.entity.QuestionLabel;
import com.joinus.knowledge.model.vo.MathLabelVO;
import com.joinus.knowledge.model.vo.MathQuestionReviewRecordVO;
import com.joinus.knowledge.model.vo.MathQuestionReviewUserStatisticsVO;
import com.joinus.knowledge.service.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Tag(name = "数学题目审核相关接口", description = "提供数学题审核相关功能")
@RestController
@RequestMapping("/review")
public class MathQuestionReviewController {

    @Resource
    private MathLabelService mathLabelService;
    @Resource
    private QuestionLabelService questionLabelService;
    @Resource
    private MathQuestionRelationshipsService mathQuestionRelationshipsService;
    @Resource
    private MathQuestionReviewRecordsService mathQuestionReviewRecordsService;


    @PostMapping("/claim")
    public Result<Integer> claimQuestions(@RequestParam("count") Integer count, @RequestParam("username") String username,
                                          @RequestParam("type") MathQuestionReviewType reviewType, @RequestParam("educationalStage") EducationalStage educationalStage) {
        Integer claimedCount = mathQuestionReviewRecordsService.claimQuestions(count, username, reviewType, educationalStage);
        return Result.success(claimedCount);
    }

    @GetMapping("/page-by-user")
    public Result<Page<MathQuestionReviewRecordVO>> page(@RequestParam(name = "page", defaultValue = "1") Integer page,
                                                         @RequestParam(name = "size", defaultValue = "10") Integer size,
                                                         @RequestParam(name = "username", required = false) String username,
                                                         @RequestParam(name = "questionId", required = false) UUID questionId,
                                                         @RequestParam(name = "status", required = false) String status,
                                                         @RequestParam(name = "reviewType") MathQuestionReviewType reviewType,
                                                         @RequestParam(name = "startDateStr", required = false) String startDateStr,
                                                         @RequestParam(name = "endDateStr", required = false) String endDateStr) {
        Page<MathQuestionReviewRecordVO> pageParam = new Page<>(page, size);

        Date startDate = null;
        Date endDate = null;
        if (StrUtil.isNotBlank(startDateStr)) {
            startDate = DateUtil.parseDate(startDateStr);
        }
        if (StrUtil.isNotBlank(endDateStr)) {
            endDate = DateUtil.offsetDay(DateUtil.parseDate(endDateStr), 1);
        }

        Page<MathQuestionReviewRecordVO> resultPage = mathQuestionReviewRecordsService.listReviewRecordsByUser(pageParam, username, questionId, status, reviewType, startDate, endDate);

        if (CollUtil.isNotEmpty(resultPage.getRecords())) {
            List<UUID> questionIds = resultPage.getRecords().stream().map(MathQuestionReviewRecordVO::getId).toList();

            List<MathLabel> labelList = mathLabelService.lambdaQuery().list();

            Map<UUID, MathLabel> labelMap = labelList.stream().collect(Collectors.toMap(MathLabel::getId, label -> label));

            Map<UUID, List<MathLabel>> questionLabelMap = questionLabelService.lambdaQuery()
                    .in(QuestionLabel::getQuestionId, questionIds)
                    .list()
                    .stream()
                    .collect(Collectors.groupingBy(QuestionLabel::getQuestionId, Collectors.mapping(QuestionLabel::getLabelId, Collectors.mapping(labelMap::get, Collectors.toList()))));

            Map<UUID, UUID> baseQuestionMap = mathQuestionRelationshipsService.lambdaQuery()
                    .in(MathQuestionRelationships::getDerivedQuestionId, questionIds)
                    .list()
                    .stream()
                    .collect(Collectors.toMap(MathQuestionRelationships::getDerivedQuestionId, MathQuestionRelationships::getBaseQuestionId));

            resultPage.getRecords().forEach(question -> {
                question.setLabels(questionLabelMap.getOrDefault(question.getId(), List.of())
                        .stream()
                        .map(MathLabelVO::ofLabel)
                        .toList());
                question.setBaseQuestionId(baseQuestionMap.get(question.getId()));
            });
        }

        return Result.success(resultPage);

    }

    @PostMapping("/verify")
    public Result<String> verify(@RequestParam("questionId") UUID questionId, @RequestParam("verified") boolean verified, @RequestParam("remark") String remark, @RequestParam("reviewType") MathQuestionReviewType reviewType, @RequestParam(value = "userName", required = false) String userName) {
        mathQuestionReviewRecordsService.verify(questionId, verified, remark, reviewType, userName);
        return Result.success("操作成功");
    }

    @GetMapping("/statistics/by-user")
    public Result<MathQuestionReviewUserStatisticsVO> userStatistics(@RequestParam("username") String username, @RequestParam("reviewType") MathQuestionReviewType reviewType) {
        return Result.success(mathQuestionReviewRecordsService.getUserStatistics(username, reviewType));
    }

    @GetMapping("/detail/{questionId}")
    public Result<MathQuestionReviewRecords> getReviewByQuestionId(@PathVariable("questionId") UUID questionId, @RequestParam("reviewType") MathQuestionReviewType reviewType) {
        MathQuestionReviewRecords mathQuestionReviewRecord =  mathQuestionReviewRecordsService.lambdaQuery()
                .eq(MathQuestionReviewRecords::getQuestionId, questionId)
                .eq(MathQuestionReviewRecords::getType, reviewType)
                .one();
        return Result.success(mathQuestionReviewRecord);
    }

    @PostMapping("/send-back/{questionId}")
    public Result<String> sendBackReview(@PathVariable("questionId") UUID questionId) {
        mathQuestionReviewRecordsService.sendBackReview(questionId);
        return Result.success("退回成功");
    }

}
