package com.joinus.knowledge.controller;

import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.model.param.GeneratedQuestionParam;
import com.joinus.knowledge.model.param.PageKnowledgePointParam;
import com.joinus.knowledge.model.param.PageQuestionParam;
import com.joinus.knowledge.service.TemporalWorkflowService;
import com.joinus.knowledge.service.impl.GenerateQuestionServiceImpl;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.CompletableFuture;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/workflow")
public class WorkflowController {

    @Resource
    private TemporalWorkflowService temporalWorkflowService;

    @Resource
    private GenerateQuestionServiceImpl generateQuestionService;

    @PostMapping("/ocr/{bookId}")
    public Result<String> ocr(@PathVariable("bookId") String bookId) {
        CompletableFuture.runAsync(() -> temporalWorkflowService.imageOcr(bookId));
        return Result.success();
    }

    @PostMapping("/extract-key-points/{bookId}")
    public Result<String> extractKeyPoints(@PathVariable("bookId") String bookId) {
        CompletableFuture.runAsync(() -> temporalWorkflowService.extractInfoFromImageByLLM(bookId));
        return Result.success();
    }

    @PostMapping("/knowledge-domain-label/{count}")
    public Result<String> knowledgeDomainLabel(@PathVariable("count") Integer count) {
        CompletableFuture.runAsync(() -> temporalWorkflowService.knowledgeDomainLabel(count));
        return Result.success();
    }

    @PostMapping("/generate-question")
    public Result<String> generateQuestion(@RequestBody GeneratedQuestionParam param) {
        CompletableFuture.runAsync(() -> temporalWorkflowService.generateQuestionBatch(param.getLabelName(), param.getCount(), param.getPublisher()));
        return Result.success();
    }

    @PostMapping("/dify/generate-question")
    public Result<String> difyGenerateQuestion(@RequestParam("count") int count) {
        CompletableFuture.runAsync(() -> generateQuestionService.generateQuestion(count));
        return Result.success();
    }

    @PostMapping("/math/dify/primary-school/generate-question/by-knowledge-point-questions")
    public Result<String> difyGeneratePrimarySchoolMathQuestionByKnowledgePointQuestions(@RequestParam("count") int count) {
        CompletableFuture.runAsync(() -> generateQuestionService.generatePrimarySchoolMathQuestionByKnowledgePoints(count));
        return Result.success();
    }

    @PostMapping("/math/dify/primary-school/generate-question/by-question-type-questions")
    public Result<String> difyGeneratePrimarySchoolMathQuestionByQuestionTypeQuestions(@RequestParam("count") int count) {
        CompletableFuture.runAsync(() -> generateQuestionService.generatePrimarySchoolMathQuestionByQuestionTypes(count));
        return Result.success();
    }

    /**
     * 高中讲义生题
     * 参数：{
              "page": 1,
              "size": 1000,
              "publisher": "REN_JIAO_A",
              "reviewed": true,
              "existHandout": true
            }
     * @param pageKnowledgePointParam
     * @return
     */
    @PostMapping("/dify/highschool/handouts-generate-question")
    public Result<String> difyGenerateQuestionHandoutsHighschool(@RequestBody PageKnowledgePointParam pageKnowledgePointParam) {
        CompletableFuture.runAsync(() -> generateQuestionService.generateQuestionHandoutsHighschool(pageKnowledgePointParam));
        return Result.success();
    }

    /**
     * 高中例题生题
     * 参数：{
                "source": "BOOK",
                "publisher": "REN_JIAO_A",
                "grade": 10,
                "semester": 1,
                "page": 1,
                "size": 4000
            }
     * @param pageQuestionParam
     * @return
     */
    @PostMapping("/dify/highschool/question-generate-question")
    public Result<String> questionGenerateQuestionHighschool(@RequestBody PageQuestionParam pageQuestionParam) {
        CompletableFuture.runAsync(() -> generateQuestionService.questionToQuestionsHighschool(pageQuestionParam));
        return Result.success();
    }

}
