package com.joinus.knowledge.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathCatalogNodes;
import com.joinus.knowledge.model.entity.Textbooks;
import com.joinus.knowledge.model.vo.MathChapterVO;
import com.joinus.knowledge.service.MathCatalogNodesService;
import com.joinus.knowledge.service.TextbooksService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 数学章节管理 Controller
 */
@RestController
@RequestMapping("/math/chapters")
@RequiredArgsConstructor
public class MathChaptersController {

    private final TextbooksService textbooksService;
    private final MathCatalogNodesService mathCatalogNodesService;

    /**
     * 查询所有章节
     * GET /api/math/chapters
     */
    @GetMapping
    public Result<List<MathChapterVO>> list(@RequestParam(value = "name", required = false) String name,
                                            @RequestParam(value = "grade", required = false) Integer grade,
                                            @RequestParam(value = "semester", required = false) Integer semester,
                                            @RequestParam(value = "publisher", required = false) PublisherType publisher) {
        List<MathChapterVO> list = mathCatalogNodesService.listChapters(name, grade, semester, publisher);
        return Result.success(list);
    }

    /**
     * 分页查询章节
     * GET /api/math/chapters/page?page=1&size=10
     */
    @GetMapping("/page")
    public Result<Page<MathCatalogNodes>> page(
            @RequestParam(name = "page", defaultValue = "1") Integer page,
            @RequestParam(name = "size", defaultValue = "10") Integer size,
            @RequestParam(name = "textbook", required = false) String textbook,
            @RequestParam(name = "semester", required = false) Integer semester) {

        Page<MathCatalogNodes> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<MathCatalogNodes> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件 - 只查询章节（level=1且textbook_id不为空）
        queryWrapper.eq(MathCatalogNodes::getLevel, 1)
                   .isNotNull(MathCatalogNodes::getTextbookId);

        LambdaQueryWrapper<Textbooks> textbooksQueryWrapper = new LambdaQueryWrapper<>();

        if (textbook != null && !textbook.isEmpty()) {
            textbooksQueryWrapper.like(Textbooks::getName, textbook);
        }

        if (semester != null) {
            textbooksQueryWrapper.eq(Textbooks::getSemester, semester);
        }

        List<Textbooks> textbooks = textbooksService.list(textbooksQueryWrapper);

        queryWrapper.in(!textbooks.isEmpty(), MathCatalogNodes::getTextbookId, textbooks.stream().map(Textbooks::getId).toList());

        // 按排序号排序
        queryWrapper.orderByAsc(MathCatalogNodes::getSortNo);

        Page<MathCatalogNodes> resultPage = mathCatalogNodesService.page(pageParam, queryWrapper);
        return Result.success(resultPage);
    }

    /**
     * 根据ID查询章节
     * GET /api/math/chapters/{id}
     */
    @GetMapping("/{id}")
    public Result<MathCatalogNodes> getById(@PathVariable("id") UUID id) {
        MathCatalogNodes mathCatalogNode = mathCatalogNodesService.getById(id);
        if (mathCatalogNode == null) {
            return Result.error(404, "章节不存在");
        }
        return Result.success(mathCatalogNode);
    }

    /**
     * 创建章节
     * POST /api/math/chapters
     */
    @PostMapping
    public Result<MathCatalogNodes> create(@RequestBody MathCatalogNodes mathCatalogNode) {
        // 设置为章节级别
        mathCatalogNode.setLevel(1);

        // 检查是否存在相同的章节（name, textbook_id都相同）
        LambdaQueryWrapper<MathCatalogNodes> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MathCatalogNodes::getName, mathCatalogNode.getName())
                   .eq(MathCatalogNodes::getTextbookId, mathCatalogNode.getTextbookId())
                   .eq(MathCatalogNodes::getLevel, 1);

        // 检查是否已存在
        MathCatalogNodes existingChapter = mathCatalogNodesService.getOne(queryWrapper);
        if (existingChapter != null) {
            // 如果已存在相同章节，返回已存在的章节
            return Result.success(existingChapter);
        }

        // ID will be automatically generated by MyBatis-Plus
        boolean success = mathCatalogNodesService.save(mathCatalogNode);
        if (success) {
            return Result.success(mathCatalogNode);
        }
        return Result.error("创建章节失败");
    }
    
    /**
     * 批量创建章节
     * POST /api/math/chapters/batch
     */
    @PostMapping("/batch")
    public Result<Boolean> batchCreate(@RequestBody List<MathCatalogNodes> mathCatalogNodesList) {
        if (mathCatalogNodesList == null || mathCatalogNodesList.isEmpty()) {
            return Result.error("章节列表不能为空");
        }

        // 过滤掉重复的章节
        List<MathCatalogNodes> chaptersToSave = new ArrayList<>();

        for (MathCatalogNodes catalogNode : mathCatalogNodesList) {
            // 设置为章节级别
            catalogNode.setLevel(1);

            // 检查是否存在相同的章节（name, textbook_id都相同）
            LambdaQueryWrapper<MathCatalogNodes> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MathCatalogNodes::getName, catalogNode.getName())
                       .eq(MathCatalogNodes::getTextbookId, catalogNode.getTextbookId())
                       .eq(MathCatalogNodes::getLevel, 1);

            // 检查是否已存在
            MathCatalogNodes existingChapter = mathCatalogNodesService.getOne(queryWrapper);
            if (existingChapter == null) {
                // 只有不存在的章节才添加到待保存列表
                chaptersToSave.add(catalogNode);
            }
        }

        if (chaptersToSave.isEmpty()) {
            // 所有章节都已存在，无需添加
            return Result.success(true);
        }

        // 批量保存章节
        boolean success = mathCatalogNodesService.saveBatch(chaptersToSave);

        if (success) {
            return Result.success(true);
        }
        return Result.error("批量创建章节失败");
    }
    
    /**
     * 更新章节
     * PUT /api/math/chapters/{id}
     */
    @PutMapping("/{id}")
    public Result<MathCatalogNodes> update(@PathVariable UUID id, @RequestBody MathCatalogNodes mathCatalogNode) {
        // 确保要更新的ID正确
        mathCatalogNode.setId(id);
        // 确保是章节级别
        mathCatalogNode.setLevel(1);
        boolean success = mathCatalogNodesService.updateById(mathCatalogNode);
        if (success) {
            return Result.success(mathCatalogNode);
        }
        return Result.error("更新章节失败");
    }

    /**
     * 删除章节
     * DELETE /api/math/chapters/{id}
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable UUID id) {
        boolean success = mathCatalogNodesService.removeById(id);
        if (success) {
            return Result.success(true);
        }
        return Result.error("删除章节失败");
    }


}
