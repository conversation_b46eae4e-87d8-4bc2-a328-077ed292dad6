package com.joinus.knowledge.controller;

import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.KnowledgePointHandoutFileType;
import com.joinus.knowledge.model.entity.MathKnowledgePointHandout;
import com.joinus.knowledge.model.entity.MathKnowledgePointHandoutFile;
import com.joinus.knowledge.model.param.AddMathHandoutParam;
import com.joinus.knowledge.model.param.MathHandoutSlideParam;
import com.joinus.knowledge.model.param.UploadFileParam;
import com.joinus.knowledge.model.vo.MathHandoutSlideshowPageVO;
import com.joinus.knowledge.model.vo.MathHandoutSlideshowVO;
import com.joinus.knowledge.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;

/**
 * 数学知识点管理 Controller
 */
@Tag(name = "数学-讲义相关", description = "数学-讲义相关")
@RestController
@RequestMapping("/math/handouts")
@RequiredArgsConstructor
public class MathHandoutController {

    private final MathKnowledgePointHandoutService handoutService;
    private final MathKnowledgePointHandoutFileService handoutFileService;
    private final MathHandoutSlideshowPresentationService slideshowPresentationService;

    @PostMapping("/batch")
    public Result<Boolean> batchAdd(@Valid @RequestBody List<AddMathHandoutParam> params) {
        handoutService.batchAdd(params);
        return Result.success();
    }

    @PostMapping("/review/{id}")
    public Result<Boolean> review(@PathVariable("id") UUID id, @RequestParam("reviewUser") String reviewUser) {
        boolean result = handoutService.lambdaUpdate()
                .set(MathKnowledgePointHandout::getReviewStatus, GlobalConstants.REVIEW_STATUS.APPROVED)
                .set(MathKnowledgePointHandout::getReviewUser, reviewUser)
                .set(MathKnowledgePointHandout::getReviewedAt, new Date())
                .eq(MathKnowledgePointHandout::getId, id)
                .update();
        return Result.success(result);
    }

    @PostMapping("/files/upload/{id}")
    public Result<Boolean> uploadFiles(@PathVariable("id") UUID id, @RequestBody List<UploadFileParam> uploadFiles) {
        handoutFileService.saveKnowledgePointHandoutFileAndRelation(id, uploadFiles);
        return Result.success(true);
    }

    @DeleteMapping("/files/clear/{id}/{type}")
    public Result<Boolean> clearFiles(@PathVariable("id") UUID id,@PathVariable("type") KnowledgePointHandoutFileType type) {
        boolean result = handoutFileService.remove(handoutFileService.lambdaQuery().eq(MathKnowledgePointHandoutFile::getHandoutId, id).eq(MathKnowledgePointHandoutFile::getType, type).getWrapper());
        return Result.success(result);
    }

    @Operation(summary = "根据讲义id查询幻灯片列表")
    @GetMapping("/{id}/slideshows")
    public Result<List<MathHandoutSlideshowVO>> listSlideshows(@PathVariable("id") UUID id) {
        List<MathHandoutSlideshowVO> results = handoutService.listSlideshows(id);
        return Result.success(results);
    }

    @Operation(summary = "根据讲义id新增幻灯片")
    @PostMapping("/{id}/slideshows")
    public Result<MathHandoutSlideshowVO> addSlideshows(@PathVariable("id") UUID id,  @RequestBody List<MathHandoutSlideParam> slideshowPages) {
        MathHandoutSlideshowVO result = handoutService.addSlideshows(id, slideshowPages);
        return Result.success(result);
    }

    @Operation(summary = "根据幻灯片id查询htmls")
    @GetMapping("/slideshows/{id}/ppt-htmls")
    public Result<List<MathHandoutSlideshowPageVO>> listPptHtmls(@PathVariable("id") UUID id) {
        List<MathHandoutSlideshowPageVO> results = slideshowPresentationService.listPptHtmlsBySlideshowId(id);
        return Result.success(results);
    }

    @Operation(summary = "根据幻灯片id更新htmls")
    @PutMapping("/slideshows/{id}/ppt-htmls")
    public Result<List<MathHandoutSlideshowPageVO>> updatePptHtmls(@PathVariable("id") UUID id, @RequestBody List<MathHandoutSlideParam> slideshowPages) {
        List<MathHandoutSlideshowPageVO> results = slideshowPresentationService.updatePptHtmls(id, slideshowPages);
        return Result.success(results);
    }

    @Operation(summary = "根据知识点id查询htmls")
    @GetMapping("/knowledge-points/{id}/ppt-htmls")
    public Result<List<MathHandoutSlideshowPageVO>> listPptHtmlsByKnowledgePointId(@PathVariable("id") UUID knowledgePointId) {
        List<MathHandoutSlideshowPageVO> results = handoutService.listPptHtmlsByKnowledgePointId(knowledgePointId);
        return Result.success(results);
    }

    @Operation(summary = "根据知识点id查询幻灯片信息")
    @GetMapping("/knowledge-points/{id}/slideshows")
    public Result<MathHandoutSlideshowVO> getSlideshowByKnowledgePointId(@PathVariable("id") UUID knowledgePointId) {
        MathHandoutSlideshowVO result = handoutService.getSlideShow(knowledgePointId);
        return Result.success(result);
    }
}
