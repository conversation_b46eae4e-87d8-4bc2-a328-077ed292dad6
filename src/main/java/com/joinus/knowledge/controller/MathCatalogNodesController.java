package com.joinus.knowledge.controller;

import cn.hutool.core.lang.Assert;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathCatalogNodes;
import com.joinus.knowledge.model.entity.SectionKnowledgePoint;
import com.joinus.knowledge.model.entity.SectionQuestionType;
import com.joinus.knowledge.model.vo.MathCatalogNodeVO;
import com.joinus.knowledge.model.vo.SimpleTreeVO;
import com.joinus.knowledge.service.MathCatalogNodesService;
import com.joinus.knowledge.service.SectionKnowledgePointsService;
import com.joinus.knowledge.service.SectionQuestionTypesService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/math/catalog/nodes")
@RequiredArgsConstructor
public class MathCatalogNodesController {

    private final MathCatalogNodesService mathCatalogNodesService;
    private final SectionKnowledgePointsService sectionKnowledgePointsService;
    private final SectionQuestionTypesService sectionQuestionTypesService;

    @GetMapping
    public Result<List<MathCatalogNodeVO>> list(@RequestParam(value = "grade", required = false) Integer grade,
                                                @RequestParam(value = "semester", required = false) Integer semester,
                                                @RequestParam(value = "publisher", required = false) PublisherType publisher) {
        List<MathCatalogNodeVO> list = mathCatalogNodesService.list(publisher, grade, semester);
        return Result.success(list);
    }

    @GetMapping("/tree")
    public Result<List<SimpleTreeVO>> tree(@RequestParam(value = "grade", required = false) Integer grade,
                                           @RequestParam(value = "semester", required = false) Integer semester,
                                           @RequestParam(value = "publisher", required = false) PublisherType publisher) {
        List<SimpleTreeVO> list = mathCatalogNodesService.tree(publisher, grade, semester);
        return Result.success(list);
    }

    @PutMapping
    public Result<UUID> addCatalogNodes(@RequestBody MathCatalogNodes catalogNodes) {
        mathCatalogNodesService.save(catalogNodes);
        return Result.success(catalogNodes.getId());
    }

    @PostMapping("/{id}")
    public Result<UUID> updateCatalogNodes(@PathVariable("id") UUID id, @RequestBody MathCatalogNodes catalogNodes) {
        catalogNodes.setId(id);
        mathCatalogNodesService.updateById(catalogNodes);
        return Result.success(catalogNodes.getId());
    }

    @DeleteMapping("/{id}")
    public Result<UUID> deleteCatalogNodes(@PathVariable("id") UUID id) {
        List<MathCatalogNodes> children = mathCatalogNodesService.lambdaQuery().eq(MathCatalogNodes::getParentId, id).list();
        Assert.isTrue(children.isEmpty(), "请先删除子节点");
        List<SectionKnowledgePoint> sectionKnowledgePoints = sectionKnowledgePointsService.lambdaQuery().eq(SectionKnowledgePoint::getSectionId, id).list();
        Assert.isTrue(sectionKnowledgePoints.isEmpty(), "节点下有知识点,无法删除");
        List<SectionQuestionType> sectionQuestionTypes = sectionQuestionTypesService.lambdaQuery().eq(SectionQuestionType::getSectionId, id).list();
        Assert.isTrue(sectionQuestionTypes.isEmpty(), "节点下有题型,无法删除");
        mathCatalogNodesService.removeById(id);
        return Result.success(id);
    }
}