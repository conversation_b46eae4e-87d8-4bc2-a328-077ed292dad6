package com.joinus.knowledge.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.common.Result;
import com.joinus.knowledge.enums.MathLabelTypeEnum;
import com.joinus.knowledge.enums.MathQuestionDimensionOptionType;
import com.joinus.knowledge.enums.MathQuestionDimensionType;
import com.joinus.knowledge.enums.QuestionSourceType;
import com.joinus.knowledge.model.dto.QuestionAnswerDTO;
import com.joinus.knowledge.model.dto.QuestionWithAnswersDTO;
import com.joinus.knowledge.model.entity.MathLabel;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.entity.MathAnswer;
import com.joinus.knowledge.model.entity.QuestionAnswerRelation;
import com.joinus.knowledge.model.param.*;
import com.joinus.knowledge.model.vo.*;
import com.joinus.knowledge.service.*;
import com.joinus.knowledge.service.impl.PdfGenerator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数学题目管理 Controller
 */
@RestController
@RequestMapping("/math/questions")
@RequiredArgsConstructor
@Slf4j
public class MathQuestionsController {

    private final MathQuestionsService mathQuestionsService;
    private final MathAnswersService mathAnswersService;
    private final QuestionAnswerRelationsService questionAnswerRelationsService;
    private final MathLabelService mathLabelService;
    private final PdfGenerator pdfGenerator;
    private final QuestionKnowledgePointsService questionKnowledgePointsService;
    private final QuestionTypesMappingService questionTypesMappingService;
    private final QuestionLabelService questionLabelService;

    /**
     * 查询所有题目
     * GET /api/math/questions
     */
    @GetMapping
    public Result<List<QuestionDetailVO>> list(@RequestParam(value = "content", required = true) String content,
                                               @RequestParam(value = "source", required = false) QuestionSourceType source) {
        return Result.success(mathQuestionsService.list(content, source));
    }

    @PostMapping("/{id}/keypoints/bind")
    public Result bindQuestionWithKeyPoint(@PathVariable("id") UUID id,
            @Valid @RequestBody BindQuestionKeyPointParam param) {
        mathQuestionsService.bindQuestionWithKeyPoint(id, param);
        return Result.success();
    }

    @PostMapping("/{id}/keypoints/change")
    public Result changeQuestionKeyPoint(@PathVariable("id") UUID id,
                                           @Valid @RequestBody BindQuestionKeyPointParam param) {
        mathQuestionsService.changeQuestionKeyPoint(id, param);
        return Result.success();
    }

    @DeleteMapping("/{id}/keypoints/unbind")
    public Result unbindQuestionWithKeyPoint(@PathVariable("id") UUID id,
                                           @Valid @RequestBody BindQuestionKeyPointParam param) {
        mathQuestionsService.unbindQuestionWithKeyPoint(id, param);
        return Result.success();
    }

    /**
     * 分页查询题目
     * GET /api/math/questions/page?page=1&size=10
     */
    @PostMapping("/page")
    public Result<Page<MathQuestionVO>> page(@RequestBody PageQuestionParam pageQuestionParam) {
        
        Page<MathQuestionVO> pageParam = new Page<>(pageQuestionParam.getPage(), pageQuestionParam.getSize());

        Page<MathQuestionVO> resultPage = mathQuestionsService.page(pageParam, pageQuestionParam);

        return Result.success(resultPage);
    }

    @PostMapping("/generate-pdf")
    public Result<String> generatePdf(@RequestBody PageQuestionParam pageQuestionParam) {

        Page<MathQuestionVO> pageParam = new Page<>(1, 50);

        Page<MathQuestionVO> resultPage = mathQuestionsService.page(pageParam, pageQuestionParam);

        List<MathQuestionVO> questionList = resultPage.getRecords();
        questionList.forEach(question -> {
            if (question.getQuestionType() != null) {
                question.setQuestionTypeName(question.getQuestionType().getType());
            }
            if (question.getSource() != null) {
                question.setSourceName(QuestionSourceType.valueOf(question.getSource()).getDescription());
            }
            question.setAnswers(mathQuestionsService.listAnswersByQuestionId(question.getId()));
        });

        String result = pdfGenerator.callNewPdfGenerationService(questionList);

        return Result.success(result);
    }

    /**
     * 根据ID查询题目
     * GET /api/math/questions/{id}
     */
    @GetMapping("/{id}")
    public Result<MathQuestion> getById(@PathVariable("id") UUID id) {
        MathQuestion question = mathQuestionsService.getById(id);
        if (question == null) {
            return Result.error(404, "题目不存在");
        }
        return Result.success(question);
    }

    @GetMapping("/{id}/detail")
    public Result<QuestionDetailVO> getDetailById(@PathVariable("id") UUID id) {
        QuestionDetailVO question = mathQuestionsService.getDetailById(id);
        return Result.success(question);
    }

    /**
     * 根据ID查询题目
     * GET /api/math/questions/{id}
     */
    @GetMapping("/{id}/answers")
    public Result<List<MathAnswerVO>> listAnswersByQuestionId(@PathVariable("id") UUID id) {
        List<MathAnswerVO> answers = mathQuestionsService.listAnswersByQuestionId(id);
        return Result.success(answers);
    }
    
    /**
     * 根据ID查询题目及其所有答案
     * GET /api/math/questions/{id}/with-answers
     */
    @GetMapping("/{id}/with-answers")
    public Result<QuestionWithAnswersDTO> getQuestionWithAnswers(@PathVariable UUID id) {
        // 1. 查询题目
        MathQuestion question = mathQuestionsService.getById(id);
        if (question == null) {
            return Result.error(404, "题目不存在");
        }
        
        // 2. 查询关系
        LambdaQueryWrapper<QuestionAnswerRelation> relationQueryWrapper = new LambdaQueryWrapper<>();
        relationQueryWrapper.eq(QuestionAnswerRelation::getQuestionId, id);
        List<QuestionAnswerRelation> relations = questionAnswerRelationsService.list(relationQueryWrapper);
        
        // 3. 创建返回对象
        QuestionWithAnswersDTO dto = new QuestionWithAnswersDTO(question);
        
        if (!relations.isEmpty()) {
            // 4. 提取所有答案ID
            List<UUID> answerIds = relations.stream()
                    .map(QuestionAnswerRelation::getAnswerId)
                    .collect(Collectors.toList());
            
            // 5. 查询所有答案
            List<MathAnswer> answers = mathAnswersService.listByIds(answerIds);
            dto.setQuestionAnswers(answers);
        }
        
        return Result.success(dto);
    }
    
    /**
     * 查询多个题目及其所有答案
     * GET /api/math/questions/with-answers
     */
    @GetMapping("/with-answers")
    public Result<List<QuestionWithAnswersDTO>> listQuestionsWithAnswers(
            @RequestParam(required = false) String questionType,
            @RequestParam(required = false) Integer difficulty,
            @RequestParam(required = false) String content) {
        
        // 1. 构建题目查询条件
        LambdaQueryWrapper<MathQuestion> questionQueryWrapper = new LambdaQueryWrapper<>();
        
        if (questionType != null && !questionType.isEmpty()) {
            questionQueryWrapper.eq(MathQuestion::getQuestionType, questionType);
        }
        
        if (difficulty != null) {
            questionQueryWrapper.eq(MathQuestion::getDifficulty, difficulty);
        }
        
        if (content != null && !content.isEmpty()) {
            questionQueryWrapper.like(MathQuestion::getContent, content);
        }
        
        // 2. 查询所有符合条件的题目
        List<MathQuestion> questions = mathQuestionsService.list(questionQueryWrapper);
        if (questions.isEmpty()) {
            return Result.success(new ArrayList<>());
        }
        
        // 3. 获取所有题目ID
        List<UUID> questionIds = questions.stream()
                .map(MathQuestion::getId)
                .collect(Collectors.toList());
        
        // 4. 查询所有关系
        LambdaQueryWrapper<QuestionAnswerRelation> relationQueryWrapper = new LambdaQueryWrapper<>();
        relationQueryWrapper.in(QuestionAnswerRelation::getQuestionId, questionIds);
        List<QuestionAnswerRelation> relations = questionAnswerRelationsService.list(relationQueryWrapper);
        
        // 5. 提取所有答案ID
        List<UUID> answerIds = relations.stream()
                .map(QuestionAnswerRelation::getAnswerId)
                .collect(Collectors.toList());
        
        // 6. 查询所有答案
        List<MathAnswer> allAnswers = answerIds.isEmpty() ?
                new ArrayList<>() : mathAnswersService.listByIds(answerIds);
        
        // 7. 建立问题ID到答案列表的映射
        Map<UUID, List<MathAnswer>> questionAnswersMap = relations.stream()
                .collect(Collectors.groupingBy(
                    QuestionAnswerRelation::getQuestionId,
                    Collectors.mapping(
                        relation -> findAnswerById(allAnswers, relation.getAnswerId()),
                        Collectors.filtering(answer -> answer != null, Collectors.toList())
                    )
                ));
        
        // 8. 构建返回结果
        List<QuestionWithAnswersDTO> result = questions.stream()
                .map(question -> {
                    QuestionWithAnswersDTO dto = new QuestionWithAnswersDTO(question);
                    List<MathAnswer> questionAnswers = questionAnswersMap.getOrDefault(question.getId(), new ArrayList<>());
                    dto.setQuestionAnswers(questionAnswers);
                    return dto;
                })
                .collect(Collectors.toList());
        
        return Result.success(result);
    }
    
    /**
     * 根据多个题目ID批量查询题目及其所有答案
     * POST /api/math/questions/batch/with-answers
     */
    @PostMapping("/batch/with-answers")
    public Result<List<QuestionWithAnswersDTO>> batchGetQuestionsWithAnswers(@RequestBody List<UUID> questionIds) {
        // 参数校验
        if (questionIds == null || questionIds.isEmpty()) {
            return Result.error("题目ID列表不能为空");
        }
        
        log.info("批量查询题目及答案，题目IDs: {}", questionIds);
        
        // 1. 查询所有题目
        List<MathQuestion> questions = mathQuestionsService.listByIds(questionIds);
        if (questions.isEmpty()) {
            return Result.success(new ArrayList<>());
        }
        
        // 2. 查询所有关系
        LambdaQueryWrapper<QuestionAnswerRelation> relationQueryWrapper = new LambdaQueryWrapper<>();
        relationQueryWrapper.in(QuestionAnswerRelation::getQuestionId, questionIds);
        List<QuestionAnswerRelation> relations = questionAnswerRelationsService.list(relationQueryWrapper);
        
        // 3. 提取所有答案ID
        List<UUID> answerIds = relations.stream()
                .map(QuestionAnswerRelation::getAnswerId)
                .collect(Collectors.toList());
        
        // 4. 查询所有答案
        List<MathAnswer> allAnswers = answerIds.isEmpty() ?
                new ArrayList<>() : mathAnswersService.listByIds(answerIds);
        
        // 5. 建立问题ID到答案列表的映射
        Map<UUID, List<MathAnswer>> questionAnswersMap = relations.stream()
                .collect(Collectors.groupingBy(
                    QuestionAnswerRelation::getQuestionId,
                    Collectors.mapping(
                        relation -> findAnswerById(allAnswers, relation.getAnswerId()),
                        Collectors.filtering(answer -> answer != null, Collectors.toList())
                    )
                ));
        
        // 6. 按照请求的ID顺序构建返回结果
        List<QuestionWithAnswersDTO> result = questions.stream()
                .map(question -> {
                    QuestionWithAnswersDTO dto = new QuestionWithAnswersDTO(question);
                    List<MathAnswer> questionAnswers = questionAnswersMap.getOrDefault(question.getId(), new ArrayList<>());
                    dto.setQuestionAnswers(questionAnswers);
                    return dto;
                })
                .collect(Collectors.toList());
        
        // 7. 按照传入的ID顺序排序结果
        Map<UUID, QuestionWithAnswersDTO> resultMap = result.stream()
                .collect(Collectors.toMap(dto -> dto.getQuestion().getId(), dto -> dto));
        
        List<QuestionWithAnswersDTO> orderedResult = questionIds.stream()
                .filter(resultMap::containsKey)
                .map(resultMap::get)
                .collect(Collectors.toList());
        
        log.info("批量查询完成，返回 {} 个题目", orderedResult.size());
        return Result.success(orderedResult);
    }
    
    /**
     * 查询所有题目，只返回id和content字段
     * GET /api/math/questions/simple
     */
    @GetMapping("/simple")
    public Result<List<Map<String, Object>>> listSimple(
            @RequestParam(required = false) String questionType,
            @RequestParam(required = false) Integer difficulty,
            @RequestParam(required = false) String content,
            @RequestParam(required = false) String createdAfter) {
        
        // 1. 构建题目查询条件
        LambdaQueryWrapper<MathQuestion> queryWrapper = new LambdaQueryWrapper<>();
        
        if (questionType != null && !questionType.isEmpty()) {
            queryWrapper.eq(MathQuestion::getQuestionType, questionType);
        }
        
        if (difficulty != null) {
            queryWrapper.eq(MathQuestion::getDifficulty, difficulty);
        }
        
        if (content != null && !content.isEmpty()) {
            queryWrapper.like(MathQuestion::getContent, content);
        }
        
        // 添加创建日期条件
        if (createdAfter != null && !createdAfter.isEmpty()) {
            try {
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                java.util.Date date = sdf.parse(createdAfter);
                queryWrapper.ge(MathQuestion::getCreatedAt, date);
            } catch (Exception e) {
                return Result.error("日期格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式");
            }
        }
        
        // 2. 查询所有符合条件的题目
        List<MathQuestion> questions = mathQuestionsService.list(queryWrapper);
        
        // 3. 只保留id和content字段
        List<Map<String, Object>> simpleResults = questions.stream()
                .map(question -> {
                    Map<String, Object> simpleQuestion = new HashMap<>();
                    simpleQuestion.put("id", question.getId());
                    simpleQuestion.put("content", question.getContent());
                    return simpleQuestion;
                })
                .collect(Collectors.toList());
        
        return Result.success(simpleResults);
    }
    
    /**
     * 从答案列表中查找指定ID的答案
     */
    private MathAnswer findAnswerById(List<MathAnswer> answers, UUID answerId) {
        return answers.stream()
                .filter(answer -> answer.getId().equals(answerId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 创建题目
     * POST /api/math/questions
     */
    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public Result<MathQuestion> create(@RequestBody MathQuestionParam question) {
        // 生成UUID
        question.generateUUID();
        boolean success = mathQuestionsService.save(question);
        if (question.getKeyPointId() != null) {
            if ("知识点".equals(question.getKeyPointType())) {
                questionKnowledgePointsService.createAssociation(question.getId(), question.getKeyPointId());
            } else {
                questionTypesMappingService.createAssociation(question.getId(), question.getKeyPointId());
            }
        }
        if (success) {
            return Result.success(question);
        }
        return Result.error("创建题目失败");
    }

    /**
     * 批量创建题目
     * POST /api/math/questions/batch
     */
    @PostMapping("/batch")
    public Result<Boolean> batchCreate(@RequestBody List<MathQuestion> questionsList) {
        if (questionsList == null || questionsList.isEmpty()) {
            return Result.error("题目列表不能为空");
        }
        
        // 为每个题目生成UUID
        questionsList.forEach(MathQuestion::generateUUID);
        
        // 批量保存题目
        boolean success = mathQuestionsService.saveBatch(questionsList);
        
        if (success) {
            return Result.success(true);
        }
        return Result.error("批量创建题目失败");
    }
    
    /**
     * 同时创建题目和答案
     * POST /api/math/questions/with-answers
     */
    @PostMapping("/with-answers")
    @Transactional
    public Result<List<QuestionAnswerDTO>> createWithAnswers(@RequestBody List<QuestionAnswerDTO> questionAnswerList) {
        if (questionAnswerList == null || questionAnswerList.isEmpty()) {
            return Result.error("题目答案列表不能为空");
        }
        
        List<MathQuestion> questions = new ArrayList<>();
        List<MathAnswer> answers = new ArrayList<>();
        List<QuestionAnswerRelation> relations = new ArrayList<>();
        
        // 准备数据
        for (QuestionAnswerDTO dto : questionAnswerList) {
            // 为题目和答案生成UUID
            MathQuestion question = dto.getQuestion();
            question.generateUUID();
            questions.add(question);
            
            MathAnswer answer = dto.getQuestionAnswer();
            answer.generateUUID();
            answers.add(answer);
            
            // 创建关联关系
            QuestionAnswerRelation relation = new QuestionAnswerRelation();
            relation.setQuestionId(question.getId());
            relation.setAnswerId(answer.getId());
            relations.add(relation);
        }
        
        // 批量保存题目
        boolean questionSuccess = mathQuestionsService.saveBatch(questions);
        if (!questionSuccess) {
            throw new RuntimeException("批量保存题目失败");
        }
        
        // 批量保存答案
        boolean answerSuccess = mathAnswersService.saveBatch(answers);
        if (!answerSuccess) {
            throw new RuntimeException("批量保存答案失败");
        }
        
        // 批量保存关联关系
        boolean relationSuccess = questionAnswerRelationsService.saveBatch(relations);
        if (!relationSuccess) {
            throw new RuntimeException("批量保存题目答案关系失败");
        }
        
        return Result.success(questionAnswerList);
    }
    
    /**
     * 更新题目
     * PUT /api/math/questions/{id}
     */
    @PutMapping("/{id}")
    public Result<QuestionDetailVO> update(@PathVariable("id") UUID id, @RequestBody UpdateQuestionParam param) {
        // 确保要更新的ID正确
        QuestionDetailVO questionDetailVO = mathQuestionsService.updateById(id, param);
        return Result.success(questionDetailVO);
    }

    /**
     * 删除题目
     * DELETE /api/math/questions/{id}
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable UUID id) {
        boolean success = mathQuestionsService.removeById(id);
        if (success) {
            return Result.success(true);
        }
        return Result.error("删除题目失败");
    }
    
    /**
     * 级联删除题目及其关联的答案
     * DELETE /api/math/questions/{id}/cascade
     */
    @DeleteMapping("/{id}/cascade")
    @Transactional
    public Result<Boolean> cascadeDelete(@PathVariable UUID id) {
        log.info("开始级联删除题目，题目ID: {}", id);
        
        // 1. 查询题目是否存在
        MathQuestion question = mathQuestionsService.getById(id);
        if (question == null) {
            return Result.error(404, "题目不存在");
        }
        
        // 2. 查询与题目关联的答案关系
        LambdaQueryWrapper<QuestionAnswerRelation> relationQueryWrapper = new LambdaQueryWrapper<>();
        relationQueryWrapper.eq(QuestionAnswerRelation::getQuestionId, id);
        List<QuestionAnswerRelation> relations = questionAnswerRelationsService.list(relationQueryWrapper);
        
        if (relations.isEmpty()) {
            log.info("题目ID: {} 没有关联的答案", id);
            // 只删除题目
            boolean success = mathQuestionsService.removeById(id);
            if (success) {
                return Result.success(true);
            }
            return Result.error("删除题目失败");
        }
        
        // 3. 提取所有关联的答案ID
        List<UUID> answerIds = relations.stream()
                .map(QuestionAnswerRelation::getAnswerId)
                .collect(Collectors.toList());
        
        log.info("题目ID: {} 关联的答案IDs: {}", id, answerIds);
        
        // 4. 删除关联关系
        boolean relationRemoveSuccess = questionAnswerRelationsService.remove(relationQueryWrapper);
        if (!relationRemoveSuccess) {
            throw new RuntimeException("删除题目与答案的关联关系失败");
        }
        
        // 5. 删除关联的答案
        boolean answerRemoveSuccess = mathAnswersService.removeByIds(answerIds);
        if (!answerRemoveSuccess) {
            throw new RuntimeException("删除关联的答案失败");
        }
        
        // 6. 删除题目
        boolean questionRemoveSuccess = mathQuestionsService.removeById(id);
        if (!questionRemoveSuccess) {
            throw new RuntimeException("删除题目失败");
        }
        
        log.info("成功级联删除题目ID: {} 及关联的 {} 个答案", id, answerIds.size());
        return Result.success(true);
    }

    @PutMapping("/{id}/error-labels")
    public Result<List<MathQuestionErrorLabelVO>> updateErrorLabels(@PathVariable("id") UUID id,
                                                                    @RequestBody List<MathQuestionErrorLabelParam> params) {
        return Result.success(mathLabelService.updateErrorLabels(id, params));
    }

    @GetMapping("/{id}/error-labels/v1")
    public Result<List<Map<String, List<MathQuestionErrorLabelVO>>>> listErrorLabelsV1(@PathVariable("id") UUID id) {
        return Result.success(mathLabelService.listErrorLabelsV1(id));
    }

    @PutMapping("/{id}/error-labels/v1")
    public Result<List<Map<String, List<MathQuestionErrorLabelVO>>>> updateErrorLabelsV1(@PathVariable("id") UUID id,
            @RequestBody List<MathQuestionErrorLabelParam> params) {
        return Result.success(mathLabelService.updateErrorLabelsV1(id, params));
    }

    @GetMapping("/{id}/common-labels/v1")
    public Result<List<MathLabelVO>> listCommonLabelsV1(@PathVariable("id") UUID id) {
        return Result.success(mathLabelService.listCommonLabelsV1(id));
    }

    @GetMapping("/{id}/parent-book-question")
    public Result<QuestionDetailVO> getParentBookQuestion(@PathVariable("id") UUID id) {
        return Result.success(mathQuestionsService.getParentBookQuestion(id));
    }

    @GetMapping("/{id}/derivative-ai-question")
    public Result<List<QuestionDetailVO>> getDerivativeAiBookQuestion(@PathVariable("id") UUID id) {
        return Result.success(mathQuestionsService.getDerivativeAiBookQuestion(id));
    }

    @GetMapping("/labels")
    public Result<List<MathLabelVO>> getLabels() {
        return Result.success(mathLabelService.list().stream().map(MathLabelVO::ofLabel).toList());
    }

    @GetMapping("/labels/with-null/v1")
    public Result<List<Map<String, Object>>> listMathLabelParamsV1() {
        List<Map<String, Object>> result = new ArrayList<>();

        List<MathLabelVO> nullLabels = mathLabelService.getLabelsWithNullV1();
        Map<String, List<MathLabelVO>> collect = nullLabels.stream().collect(Collectors.groupingBy(MathLabelVO::getType));
        for (Map.Entry<String, List<MathLabelVO>> entry : collect.entrySet()) {
            String type = entry.getKey();
            HashMap<String, Object> item = new HashMap<>();
            item.put("key", type);
            item.put("value", MathLabelTypeEnum.ofType(type).getDescription());
            item.put("names", entry.getValue());
            result.add(item);
        }
        return Result.success(result);
    }

    @GetMapping("/labels/knowledge-domain")
    public Result<List<MathLabel>> getKnowledgeDomainLabels() {
        List<MathLabel> labels = mathLabelService.listKnowledgeDomain();
        return Result.success(labels);
    }

    @PutMapping("/enabled")
    public Result updateEnabled(@RequestBody @Valid UpdateQuestionEnabledParam param) {
        mathQuestionsService.updateEnabled(param);
        return Result.success();
    }

    @Operation(summary = "根据题目id获取题目知识点和题型列表")
    @Parameters({
            @Parameter(name = "id", description = "题目id", required = true, in = ParameterIn.PATH, example = "7983e859-b710-404f-a808-6f2f2518873d")
    })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功解析题目坐标", content = @Content(mediaType = "application/json", schema = @Schema(implementation = MathKnowledgePointVO.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/{id}/knowledge-points-and-question-types")
    public Result<KnowledgePointAndQuestionTypeVO> listKnowledgePointsAndQuestionTypes(@PathVariable("id") UUID id) {
        KnowledgePointAndQuestionTypeVO vo = mathQuestionsService.listKnowledgePointsAndQuestionTypes(id);
        return Result.success(vo);
    }

    @GetMapping("/{id}/dimensions")
    public Result<List<MathQuestionDimensionVO>> listMathQuestionDimensions(@PathVariable("id") UUID id) {
        List<MathQuestionDimensionVO> dimensions = mathQuestionsService.listMathQuestionDimensions(id);
        return Result.success(dimensions);
    }

    @DeleteMapping("/{id}/answers/{answer-id}")
    public Result deleteAnswer(@PathVariable("id") UUID id,
                               @PathVariable("answer-id") UUID answerId) {
        mathQuestionsService.deleteAnswer(id, answerId);
        return Result.success();
    }

    @GetMapping("/dimensions")
    public Result<List<Map<String, Object>>> listMathQuestionDimensions() {
        List<Map<String, Object>> result = new ArrayList<>();
        for (MathQuestionDimensionType type : MathQuestionDimensionType.values()) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("key", type.name());
            map.put("value", type.getDescription());
            List<Map<String, String>> options = new ArrayList<>();
            type.getOptions().stream().forEach(option -> {
                if (option == MathQuestionDimensionOptionType.UNKNOWN) {
                    return;
                }
                HashMap<String, String> optionMap = new HashMap<>();
                optionMap.put("key", option.name());
                optionMap.put("value", option.getDescription());
                options.add(optionMap);
            });
            map.put("options", options);
            result.add(map);
        }
        return Result.success(result);
    }


    @Operation(summary = "生成pdf参数")
    @PostMapping("/pdf/params")
    public List<MathQuestionVO> generatePdfParametersByParams(@RequestBody PageQuestionParam pageQuestionParam) {
        Page<MathQuestionVO> pageParam = new Page<>(1, 50);
        Page<MathQuestionVO> resultPage = mathQuestionsService.page(pageParam, pageQuestionParam);
        List<MathQuestionVO> questionList = resultPage.getRecords();
        questionList.forEach(question -> {
            if (question.getQuestionType() != null) {
                question.setQuestionTypeName(question.getQuestionType().getType());
            }
            if (question.getSource() != null) {
                question.setSourceName(QuestionSourceType.valueOf(question.getSource()).getDescription());
            }
            question.setAnswers(mathQuestionsService.listAnswersByQuestionId(question.getId()));
        });
        return questionList;
    }

    @PostMapping("/ai/save")
    public Result<String> saveAIQuestion(@RequestBody MathGeneratedQuestionsParam questionsParam) {
        mathQuestionsService.saveAIQuestion(questionsParam);
        return Result.success();
    }

    @PostMapping("/{id}/labels")
    public Result<String> saveLabels(@PathVariable("id") UUID id, @RequestBody List<UUID> labelIds) {
        questionLabelService.saveRelations(id, labelIds);
        return Result.success();
    }
}
