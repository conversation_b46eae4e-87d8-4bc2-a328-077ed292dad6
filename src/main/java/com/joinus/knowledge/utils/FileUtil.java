package com.joinus.knowledge.utils;

import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 文件工具类
 */
public class FileUtil {

    /**
     * 常见文件类型的MIME类型映射
     */
    private static final Map<String, String> MIME_TYPE_MAP = new HashMap<>();

    static {
        // 图片类型
        MIME_TYPE_MAP.put("jpg", "image/jpeg");
        MIME_TYPE_MAP.put("jpeg", "image/jpeg");
        MIME_TYPE_MAP.put("png", "image/png");
        MIME_TYPE_MAP.put("gif", "image/gif");
        MIME_TYPE_MAP.put("bmp", "image/bmp");
        MIME_TYPE_MAP.put("webp", "image/webp");
        MIME_TYPE_MAP.put("svg", "image/svg+xml");
        MIME_TYPE_MAP.put("avif", "image/avif");
        
        // 文档类型
        MIME_TYPE_MAP.put("pdf", "application/pdf");
        MIME_TYPE_MAP.put("doc", "application/msword");
        MIME_TYPE_MAP.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        MIME_TYPE_MAP.put("xls", "application/vnd.ms-excel");
        MIME_TYPE_MAP.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        MIME_TYPE_MAP.put("ppt", "application/vnd.ms-powerpoint");
        MIME_TYPE_MAP.put("pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation");
        MIME_TYPE_MAP.put("txt", "text/plain");
        MIME_TYPE_MAP.put("rtf", "application/rtf");
        
        // 音频类型
        MIME_TYPE_MAP.put("mp3", "audio/mpeg");
        MIME_TYPE_MAP.put("wav", "audio/wav");
        MIME_TYPE_MAP.put("ogg", "audio/ogg");
        MIME_TYPE_MAP.put("flac", "audio/flac");
        MIME_TYPE_MAP.put("aac", "audio/aac");
        
        // 视频类型
        MIME_TYPE_MAP.put("mp4", "video/mp4");
        MIME_TYPE_MAP.put("avi", "video/x-msvideo");
        MIME_TYPE_MAP.put("wmv", "video/x-ms-wmv");
        MIME_TYPE_MAP.put("flv", "video/x-flv");
        MIME_TYPE_MAP.put("mov", "video/quicktime");
        MIME_TYPE_MAP.put("webm", "video/webm");
        MIME_TYPE_MAP.put("mkv", "video/x-matroska");
        
        // 压缩文件类型
        MIME_TYPE_MAP.put("zip", "application/zip");
        MIME_TYPE_MAP.put("rar", "application/x-rar-compressed");
        MIME_TYPE_MAP.put("7z", "application/x-7z-compressed");
        MIME_TYPE_MAP.put("tar", "application/x-tar");
        MIME_TYPE_MAP.put("gz", "application/gzip");
        
        // 网页类型
        MIME_TYPE_MAP.put("html", "text/html");
        MIME_TYPE_MAP.put("htm", "text/html");
        MIME_TYPE_MAP.put("css", "text/css");
        MIME_TYPE_MAP.put("js", "application/javascript");
        MIME_TYPE_MAP.put("json", "application/json");
        MIME_TYPE_MAP.put("xml", "application/xml");
    }

    /**
     * 根据文件名获取文件后缀（不包含点号）
     *
     * @param fileName 文件名
     * @return 文件后缀，如果没有后缀或文件名为空则返回空字符串
     */
    public static String getFileExtension(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            return "";
        }
        
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex == -1 || dotIndex == fileName.length() - 1) {
            return "";
        }
        
        return fileName.substring(dotIndex + 1).toLowerCase();
    }

    /**
     * 根据文件后缀获取MIME类型
     *
     * @param extension 文件后缀（不包含点号）
     * @return MIME类型，如果未知则返回通用二进制类型 application/octet-stream
     */
    public static String getMimeTypeByExtension(String extension) {
        if (!StringUtils.hasText(extension)) {
            return "application/octet-stream";
        }
        
        String lowerExtension = extension.toLowerCase();
        return MIME_TYPE_MAP.getOrDefault(lowerExtension, "application/octet-stream");
    }

    /**
     * 根据文件名获取MIME类型
     *
     * @param fileName 文件名
     * @return MIME类型，如果未知则返回通用二进制类型 application/octet-stream
     */
    public static String getMimeTypeByFileName(String fileName) {
        String extension = getFileExtension(fileName);
        return getMimeTypeByExtension(extension);
    }

    /**
     * 根据OSS Key获取文件名
     * 文件名为最后一个/到?之间的内容
     * 例如：/a/b/c.jpg?size=5 -> c.jpg
     *
     * @param ossKey OSS Key或URL
     * @return 文件名，如果无法解析则返回空字符串
     */
    public static String getFileNameFromOssKey(String ossKey) {
        if (!StringUtils.hasText(ossKey)) {
            return "";
        }
        
        // 找到最后一个斜杠的位置
        int lastSlashIndex = ossKey.lastIndexOf('/');
        if (lastSlashIndex == -1) {
            // 没有斜杠，整个字符串可能是文件名
            lastSlashIndex = 0;
        } else {
            // 跳过斜杠
            lastSlashIndex++;
        }
        
        // 找到问号的位置
        int questionMarkIndex = ossKey.indexOf('?', lastSlashIndex);
        if (questionMarkIndex == -1) {
            // 没有问号，从最后一个斜杠到字符串结束
            return ossKey.substring(lastSlashIndex);
        } else {
            // 从最后一个斜杠到问号
            return ossKey.substring(lastSlashIndex, questionMarkIndex);
        }
    }

    public static void main(String[] args) {
        String ossKey = "/api/a.mp3";
        System.out.println(getFileNameFromOssKey(ossKey));
        System.out.println(getMimeTypeByFileName(ossKey));
        System.out.println(getFileExtension(ossKey));
    }
}
