package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.entity.EnglishAiQuestion;
import com.joinus.knowledge.model.param.EnableOrDisableEnglishAiQuestionParam;
import com.joinus.knowledge.model.param.EnglishAiQuestionPageQueryParam;
import com.joinus.knowledge.model.vo.EnglishAiQuestionDetailVO;
import com.joinus.knowledge.model.vo.EnglishAiQuestionVO;

import java.util.UUID;

/**
 * <AUTHOR>
 * @description 针对表【english_ai_question(英语ai试题表)】的数据库操作Service
 * @createDate 2025-09-01 18:15:37
 */
public interface EnglishAiQuestionService extends IService<EnglishAiQuestion> {

    IPage<EnglishAiQuestionVO> pageQuery(EnglishAiQuestionPageQueryParam param);

    EnglishAiQuestionDetailVO queryDetailById(UUID id);

    void batchEnable(EnableOrDisableEnglishAiQuestionParam param);

    void batchDisable(EnableOrDisableEnglishAiQuestionParam param);
}
