package com.joinus.knowledge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.config.base.BusinessException;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.mapper.EnglishExamMapper;
import com.joinus.knowledge.model.entity.*;
import com.joinus.knowledge.model.param.*;
import com.joinus.knowledge.model.vo.*;
import com.joinus.knowledge.service.*;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description 针对表【en_flow_exam(英语试卷表)】的数据库操作Service实现
 * @createDate 2025-08-01 18:15:37
 */
@Service
public class EnglishExamServiceImpl extends ServiceImpl<EnglishExamMapper, EnglishExam>
        implements EnglishExamService {

    @Resource
    private OssService ossService;

    @Override
    public IPage<EnglishExamVO> pageQuery(EnglishExamPageQueryParam param) {
        Page<EnglishExamVO> page = new Page<>(param.getPage(), param.getSize());
        return baseMapper.pageQuery(page, param);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateExamDetail(UUID id, UpdateEnglishExamParam param) {
        EnglishExam englishExam = baseMapper.selectById(id);
        if (Objects.isNull(englishExam)) {
            throw new BusinessException("试卷不存在");
        }

        englishExam = BeanUtil.copyProperties(param, EnglishExam.class);
        englishExam.setId(id);
        baseMapper.updateById(englishExam);
    }

    @Override
    public EnglishExamVO queryExamDetailById(UUID id) {
        EnglishExam englishExam = baseMapper.selectById(id);
        if (Objects.isNull(englishExam)) {
            throw new BusinessException("试卷不存在");
        }

        EnglishExamVO englishExamVO = BeanUtil.copyProperties(englishExam, EnglishExamVO.class);
        if (StrUtil.isNotBlank(englishExam.getOssUrls())) {
            List<String> ossKeyList = JSONUtil.toList(JSONUtil.parseArray(englishExam.getOssUrls()), String.class);
            List<String> ossUrlList = ossKeyList.stream().map(ossKey -> {
                OssFileVO ossFileVO = ossService.getPresignedInfo(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB, ossKey);
                return ossFileVO.getPresignedUrl();
            }).toList();
            englishExamVO.setImages(ossUrlList);
        }
        return englishExamVO;
    }

    @Override
    public List<String> listSchool(String schoolName) {
        List<String> schools = baseMapper.listSchool(schoolName);
        return schools.stream().filter(StrUtil::isNotBlank).toList();
    }
}
