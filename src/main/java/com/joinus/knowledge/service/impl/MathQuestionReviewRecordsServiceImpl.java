package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.enums.EducationalStage;
import com.joinus.knowledge.enums.MathQuestionReviewStatus;
import com.joinus.knowledge.enums.MathQuestionReviewType;
import com.joinus.knowledge.model.dto.CandidateQuestion;
import com.joinus.knowledge.model.dto.KeyPointStats;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.entity.MathQuestionReviewRecords;
import com.joinus.knowledge.model.vo.MathQuestionReviewRecordVO;
import com.joinus.knowledge.model.vo.MathQuestionReviewUserStatisticsVO;
import com.joinus.knowledge.service.MathQuestionReviewRecordsService;
import com.joinus.knowledge.mapper.MathQuestionReviewRecordsMapper;
import com.joinus.knowledge.service.MathQuestionsService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【math_question_review_records(数学题审核记录表)】的数据库操作Service实现
* @createDate 2025-06-16 16:25:44
*/
@Service
@Slf4j
public class MathQuestionReviewRecordsServiceImpl extends ServiceImpl<MathQuestionReviewRecordsMapper, MathQuestionReviewRecords>
    implements MathQuestionReviewRecordsService{

    @Value("${review.pending.limit:100}")
    private Integer reviewPendingLimit;

    @Resource
    private MathQuestionsService mathQuestionsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int claimQuestions(int count, String username, MathQuestionReviewType reviewType, EducationalStage educationalStage) {
        Assert.isTrue(educationalStage != null, "请选择抽取的学段");
        Assert.isTrue(count > 0, "抽取数量必须大于0");
        Assert.isTrue(count <= reviewPendingLimit, "抽取数量不能超过" + reviewPendingLimit);
        //查询当前用户未审核的数据量
        int pendingCount = this.lambdaQuery()
                .eq(MathQuestionReviewRecords::getUsername, username)
                .eq(MathQuestionReviewRecords::getType, reviewType)
                .eq(MathQuestionReviewRecords::getStatus, GlobalConstants.REVIEW_STATUS.PENDING)
                .count()
                .intValue();
        Assert.isTrue(pendingCount < reviewPendingLimit, "用户已抽取未审核的题目过多，请先审核");
        count = Math.min(count, reviewPendingLimit - pendingCount);
        List<UUID> questionIds = this.extractPreferredQuestionsForReview(count, reviewType, username, educationalStage);
        if (CollUtil.isEmpty(questionIds)) {
            return 0;
        }
        List<MathQuestion> questionList = mathQuestionsService.lambdaQuery()
                .in(MathQuestion::getId, questionIds)
                .last("LIMIT " + count + " FOR UPDATE SKIP LOCKED")
                .list();
        if (CollUtil.isEmpty(questionList)) {
            return 0;
        }

        List<MathQuestionReviewRecords> reviewRecords = new ArrayList<>();
        questionList.forEach(mathQuestion -> {
            MathQuestionReviewRecords reviewRecord = new MathQuestionReviewRecords();
            reviewRecord.setQuestionId(mathQuestion.getId());
            reviewRecord.setUsername(username);
            reviewRecord.setType(reviewType);
            reviewRecord.setStatus(GlobalConstants.REVIEW_STATUS.PENDING);
            reviewRecords.add(reviewRecord);
            mathQuestion.setReviewStatus(MathQuestionReviewStatus.ofPendingAndReviewType(reviewType));
        });

        this.saveBatch(reviewRecords);
        mathQuestionsService.updateBatchById(questionList);

        return reviewRecords.size();
    }

    @Override
    public Page<MathQuestionReviewRecordVO> listReviewRecordsByUser(Page<MathQuestionReviewRecordVO> pageParam, String username, UUID questionId, String status, MathQuestionReviewType reviewType, Date startDate, Date endDate) {
        Page<MathQuestionReviewRecordVO> page = baseMapper.listReviewRecordsByUser(pageParam, username, questionId, status, reviewType, startDate, endDate);
        // 处理结果内容解码
        page.getRecords().forEach(item -> {
            item.setContent(mathQuestionsService.decodeContentV2(item.getContent()));
        });
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void verify(UUID questionId, boolean verified, String remark, MathQuestionReviewType reviewType, String userName) {
        long count = this.lambdaQuery()
                .eq(MathQuestionReviewRecords::getQuestionId, questionId)
                .eq(MathQuestionReviewRecords::getType, reviewType)
                .eq(MathQuestionReviewRecords::getStatus, GlobalConstants.REVIEW_STATUS.PENDING)
                .count();
        if (count == 0 && reviewType == MathQuestionReviewType.FIRST_REVIEW) {
            MathQuestionReviewRecords reviewRecord = new MathQuestionReviewRecords();
            reviewRecord.setQuestionId(questionId);
            reviewRecord.setType(reviewType);
            reviewRecord.setReviewedAt(new Date());
            reviewRecord.setStatus(verified ? GlobalConstants.REVIEW_STATUS.APPROVED : GlobalConstants.REVIEW_STATUS.REJECTED);
            reviewRecord.setRemark(remark);
            if (StrUtil.isNotBlank(userName)) {
                reviewRecord.setUsername(userName);
            }
            this.save(reviewRecord);
        } else if (count > 0) {
            this.lambdaUpdate()
                    .set(MathQuestionReviewRecords::getStatus, verified ? GlobalConstants.REVIEW_STATUS.APPROVED : GlobalConstants.REVIEW_STATUS.REJECTED)
                    .set(MathQuestionReviewRecords::getRemark, remark)
                    .set(MathQuestionReviewRecords::getReviewedAt, new Date())
                    .eq(MathQuestionReviewRecords::getQuestionId, questionId)
                    .eq(MathQuestionReviewRecords::getType, reviewType)
                    .eq(StrUtil.isNotBlank(userName), MathQuestionReviewRecords::getUsername, userName)
                    .update();
        } else {
            throw new IllegalStateException("审核状态错误");
        }
        mathQuestionsService.lambdaUpdate()
                .set(MathQuestion::getReviewStatus, MathQuestionReviewStatus.ofVerifiedAndReviewType(verified, reviewType))
                .set(MathQuestion::getUpdatedAt, new Date())
                .eq(MathQuestion::getId, questionId)
                .update();
    }

    @Override
    public MathQuestionReviewUserStatisticsVO getUserStatistics(String username, MathQuestionReviewType reviewType) {
        return baseMapper.getUserStatistics(username, reviewType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendBackReview(UUID questionId) {
        MathQuestion mathQuestion = mathQuestionsService.getById(questionId);
        MathQuestionReviewType currentReviewType = mathQuestion.getReviewStatus().getReviewType();

        this.lambdaUpdate()
                .set(MathQuestionReviewRecords::getStatus, GlobalConstants.REVIEW_STATUS.PENDING)
                .set(MathQuestionReviewRecords::getReviewedAt, null)
                .eq(MathQuestionReviewRecords::getQuestionId, questionId)
                .eq(MathQuestionReviewRecords::getType, currentReviewType)
                .update();

        mathQuestionsService.lambdaUpdate()
                .set(MathQuestion::getReviewStatus, MathQuestionReviewStatus.ofPendingAndReviewType(currentReviewType))
                .set(MathQuestion::getUpdatedAt, new Date())
                .eq(MathQuestion::getId, questionId)
                .update();
    }

    public List<UUID> extractPreferredQuestionsForReview(int numberOfItemsToExtract, MathQuestionReviewType reviewType, String username, EducationalStage educationalStage) {
        // --- 1. 初始化状态 ---
        // 假设 baseMapper.getInitialKeyPointStats() 返回 List<Map<String, Object>>
        // 每个 Map 包含: "keyPointId", "successCount", "totalCount"
        Map<UUID, KeyPointStats> keyPointsStatsMap = baseMapper.getInitialKeyPointStats(reviewType.name(), educationalStage).stream()
                .collect(Collectors.toMap(
                        stats -> (UUID) stats.get("keyPointId"),
                        stats -> new KeyPointStats(
                                ((Number) stats.getOrDefault("successCount", 0)).intValue(),
                                ((Number) stats.getOrDefault("totalCount", 0)).intValue()
                        )
                ));

        log.debug("Initial key point stats: {}", keyPointsStatsMap);

        // 一次性获取所有可能的候选题，避免在循环中查询数据库
        List<CandidateQuestion> allCandidates = baseMapper.getAllCandidatesWithKeyPoints(username, reviewType.name(), educationalStage);
        Set<UUID> selectedQuestionIds = new HashSet<>();

        // --- 2. 循环抽取 ---
        for (int i = 0; i < numberOfItemsToExtract; i++) {
            List<CandidateQuestion> availableCandidates = allCandidates.stream()
                    .filter(c -> !selectedQuestionIds.contains(c.getQuestionId()) && !c.getKeyPointIds().isEmpty())
                    .toList();

            if (availableCandidates.isEmpty()) {
                log.info("No more available candidates.");
                break;
            }

            CandidateQuestion bestCandidate = null;
            // 用于存储最佳候选题的分数，以便比较
            long bestSuccessScore = Long.MAX_VALUE;
            long bestTotalScore = Long.MAX_VALUE;

            // --- 3. 核心比较逻辑：寻找最佳候选题 ---
            for (CandidateQuestion candidate : availableCandidates) {
                long minSuccessScore = Long.MAX_VALUE;
                long minTotalScore = Long.MAX_VALUE;

                // 计算当前候选题的两个分数维度：其关联知识点中的最小成功数和最小总数
                for (UUID kpId : candidate.getKeyPointIds()) {
                    KeyPointStats stats = keyPointsStatsMap.getOrDefault(kpId, new KeyPointStats(0, 0));
                    minSuccessScore = Math.min(minSuccessScore, stats.getSuccessCount());
                    minTotalScore = Math.min(minTotalScore, stats.getTotalCount());
                }

                // ---- 多维度比较逻辑 ----
                // 规则1：优先选择“最小成功数”更低的
                if (minSuccessScore < bestSuccessScore) {
                    bestSuccessScore = minSuccessScore;
                    bestTotalScore = minTotalScore;
                    bestCandidate = candidate;
                }
                // 规则2：如果“最小成功数”相同，则选择“最小总数”更低的
                else if (minSuccessScore == bestSuccessScore) {
                    if (minTotalScore < bestTotalScore) {
                        bestTotalScore = minTotalScore;
                        bestCandidate = candidate;
                    }
                }
            }

            if (bestCandidate == null) {
                // 如果所有可用候选题的知识点都不在Map中，可能需要一个备用选择，例如随机选择
                bestCandidate = availableCandidates.getFirst();
                log.warn("No best candidate found based on scores, picking first available one.");
            }

            // --- 4. 更新状态 ---
            selectedQuestionIds.add(bestCandidate.getQuestionId());
            log.debug("Iteration {}: Selected Question ID: {}, SuccessScore: {}, TotalScore: {}",
                    i + 1, bestCandidate.getQuestionId(), bestSuccessScore, bestTotalScore);

            // 根据需求“被选中的候选人加到抽取的数量上”，我们增加总数
            for (UUID keyPointId : bestCandidate.getKeyPointIds()) {
                KeyPointStats stats = keyPointsStatsMap.computeIfAbsent(keyPointId, k -> new KeyPointStats(0, 0));
                stats.setTotalCount(stats.getTotalCount() + 1);
            }
            log.debug("Updated key point stats: {}", keyPointsStatsMap);
        }

        return new ArrayList<>(selectedQuestionIds);
    }


}




