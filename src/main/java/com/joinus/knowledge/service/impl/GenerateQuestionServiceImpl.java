package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.Header;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.joinus.knowledge.mapper.MathQuestionsMapper;
import com.joinus.knowledge.model.dto.ImageData;
import com.joinus.knowledge.model.entity.MathQuestion;
import com.joinus.knowledge.model.entity.QuestionAnswerRelation;
import com.joinus.knowledge.model.param.DifyGenerateQuestionParam;
import com.joinus.knowledge.model.param.DifyHighSchoolGenerateQuestionParam;
import com.joinus.knowledge.model.param.DifyHighSchoolQToQuestionParam;
import com.joinus.knowledge.model.param.DifyImageParam;
import com.joinus.knowledge.model.param.DifyParam;
import com.joinus.knowledge.model.param.PageKnowledgePointParam;
import com.joinus.knowledge.model.param.PageQuestionParam;
import com.joinus.knowledge.model.po.AIGeneratedQuestionCount;
import com.joinus.knowledge.model.vo.FileVO;
import com.joinus.knowledge.model.vo.MathAnswerVO;
import com.joinus.knowledge.model.vo.MathKnowledgePointVO;
import com.joinus.knowledge.model.vo.MathQuestionVO;
import com.joinus.knowledge.model.vo.QuestionAnswerDetailVO;
import com.joinus.knowledge.model.vo.QuestionDetailVO;
import com.joinus.knowledge.service.FilesService;
import com.joinus.knowledge.service.MathAnswersService;
import com.joinus.knowledge.service.MathKnowledgePointsService;
import com.joinus.knowledge.service.MathQuestionsService;
import com.joinus.knowledge.service.QuestionAnswerRelationsService;
import com.joinus.knowledge.utils.ImageTagExtractor;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GenerateQuestionServiceImpl {
    @Resource
    private MathQuestionsMapper mathQuestionsMapper;

    @Resource
    private MathQuestionsService mathQuestionsService;

    @Resource
    private MathAnswersService mathAnswersService;

    @Resource
    private QuestionAnswerRelationsService questionAnswerRelationsService;

    @Resource
    private WebClient difyWebClient;

    @Resource
    private MathKnowledgePointsService mathKnowledgePointsService;
    @Resource
    private FilesService filesService;

    @Value("${dify.workflow.generate-math-question:app-D9hr5KamYhIK3Nc4omEuEUBq}")
    private String apiKey;
    @Value("${dify.workflow.high-school.handout-math-question:app-lB9dXpRHRsmDGwLYyRug95B3}")
    private String highSchoolApiKey;
    @Value("${dify.workflow.high-school.question-to-question:app-lEwzh3ZyMSZ2XC35Vmgrk0DW}")
    private String highSchoolQToQApiKey;
    @Value("${dify.workflow.generate-math-question.primary-school.by-knowledge-points:app-11iz4Dw1BEGaT9qJk2zHAi0x}")
    private String primaryApiKeyByKnowledgePoints;
    @Value("${dify.workflow.generate-math-question.primary-school.by-question-types:app-Brsl2UEWsVZ85bTP43SE1yOF}")
    private String primaryApiKeyByQuestionTypes;

    int corePoolSize = 10;
    int maximumPoolSize = 10;
    long keepAliveTime = 0L;
    int queueCapacity = 5000;

    BlockingQueue<Runnable> workQueue = new ArrayBlockingQueue<>(queueCapacity);

    ThreadPoolExecutor executor = new ThreadPoolExecutor(
            corePoolSize,
            maximumPoolSize,
            keepAliveTime,
            TimeUnit.MILLISECONDS,
            workQueue,
            new ThreadPoolExecutor.AbortPolicy());

    public void generateQuestion(int count) {
        List<AIGeneratedQuestionCount> list = mathQuestionsMapper.listNotEnoughAIGeneratedQuestionCount(count);
        list.forEach(questionCount -> executor.execute(() -> generate(questionCount, count)));
    }

    private void generate(AIGeneratedQuestionCount aiGeneratedQuestionCount, int limitCount) {
        int generateCount = Math.min(limitCount - aiGeneratedQuestionCount.getTotalCount(), 5);

        MathQuestion mathQuestion = mathQuestionsMapper.selectById(aiGeneratedQuestionCount.getQuestionId());
        QuestionAnswerRelation questionAnswerRelation = questionAnswerRelationsService.lambdaQuery()
                .eq(QuestionAnswerRelation::getQuestionId, aiGeneratedQuestionCount.getQuestionId())
                .list()
                .getFirst();
        QuestionAnswerDetailVO answerVO = mathAnswersService.getById(questionAnswerRelation.getAnswerId());
        DifyGenerateQuestionParam generateQuestionParam = DifyGenerateQuestionParam.builder()
                .questionId(aiGeneratedQuestionCount.getQuestionId().toString())
                .content(mathQuestion.getContent())
                .type(mathQuestion.getQuestionType().getType())
                .answerId(answerVO.getId().toString())
                .answer(answerVO.getAnswer())
                .analysis(answerVO.getContent())
                .numberOfQuestions(String.valueOf(generateCount))
                .build();
        QuestionDetailVO questionDetailVO = mathQuestionsService
                .getDetailById(aiGeneratedQuestionCount.getQuestionId());
        List<DifyImageParam> imageList = new ArrayList<>();
        List<FileVO> fileVOList = questionDetailVO.getFiles();
        if (CollUtil.isNotEmpty(fileVOList)) {
            for (FileVO fileVO : fileVOList) {
                DifyImageParam difyImageParam = DifyImageParam.builder()
                        .transferMethod("remote_url")
                        .type("image")
                        .url(fileVO.getOssUrl())
                        .build();
                imageList.add(difyImageParam);
            }
            generateQuestionParam.setContentImage(imageList);
        }
        DifyParam difyParam = DifyParam.builder()
                .inputs(generateQuestionParam)
                .responseMode("streaming")
                .user("edu-knowledge-manage")
                .build();
        try {
            String result = difyWebClient.post()
                    .uri("/workflows/run")
                    .header(Header.AUTHORIZATION.getValue(), "Bearer " + apiKey)
                    .bodyValue(difyParam)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(1200))
                    .onErrorResume(e -> {
                        log.error("调用dify生题失败: questionId={}, 错误={}", mathQuestion.getId(), e.getMessage(), e);
                        return Mono.just("生题失败: " + e.getMessage());
                    })
                    .block();
        } catch (Exception e) {
            log.error("调用dify生题失败: questionId={}, 错误={}", mathQuestion.getId(), e.getMessage(), e);
        }
    }

    public void generatePrimarySchoolMathQuestionByKnowledgePoints(int count) {
        List<AIGeneratedQuestionCount> baseQuestions = mathQuestionsMapper.listPrimarySchoolBaseQuestionByKnowledgePoints(count);
        baseQuestions.forEach(questionCount -> executor.execute(() -> generatePrimaryMathQuestionByKnowledgePoints(questionCount, count)));
    }

    private void generatePrimaryMathQuestionByKnowledgePoints(AIGeneratedQuestionCount aiGeneratedQuestionCount, int limitCount) {
        int generateCount = Math.min(limitCount - aiGeneratedQuestionCount.getTotalCount(), 15);

        MathQuestion mathQuestion = mathQuestionsMapper.selectById(aiGeneratedQuestionCount.getQuestionId());
        QuestionAnswerRelation questionAnswerRelation = questionAnswerRelationsService.lambdaQuery()
                .eq(QuestionAnswerRelation::getQuestionId, aiGeneratedQuestionCount.getQuestionId())
                .list()
                .getFirst();
        QuestionAnswerDetailVO answerVO = mathAnswersService.getById(questionAnswerRelation.getAnswerId());
        DifyGenerateQuestionParam generateQuestionParam = DifyGenerateQuestionParam.builder()
                .questionId(aiGeneratedQuestionCount.getQuestionId().toString())
                .content(mathQuestion.getContent())
                .type(mathQuestion.getQuestionType() == null ? "" : mathQuestion.getQuestionType().getType())
                .answer(answerVO.getAnswer())
                .analysis(answerVO.getContent())
                .numberOfQuestions(String.valueOf(generateCount))
                .knowledgePointName(aiGeneratedQuestionCount.getKnowledgePointName())
                .knowledgePointId(aiGeneratedQuestionCount.getKnowledgePointId().toString())
                .build();
        QuestionDetailVO questionDetailVO = mathQuestionsService.getDetailById(aiGeneratedQuestionCount.getQuestionId());
        List<DifyImageParam> imageList = new ArrayList<>();
        List<FileVO> fileVOList = questionDetailVO.getFiles();
        if (CollUtil.isNotEmpty(fileVOList)) {
            for (FileVO fileVO : fileVOList) {
                DifyImageParam difyImageParam = DifyImageParam.builder()
                        .transferMethod("remote_url")
                        .type("image")
                        .url(fileVO.getOssUrl())
                        .build();
                imageList.add(difyImageParam);
            }
            generateQuestionParam.setContentImage(imageList);
        }
        DifyParam difyParam = DifyParam.builder()
                .inputs(generateQuestionParam)
                .responseMode("streaming")
                .user("edu-knowledge-manage")
                .build();
        try {
            String result = difyWebClient.post()
                    .uri("/workflows/run")
                    .header(Header.AUTHORIZATION.getValue(),"Bearer " + primaryApiKeyByKnowledgePoints)
                    .bodyValue(difyParam)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(1200))
                    .onErrorResume(e -> {
                        log.error("调用dify生题失败: questionId={}, 错误={}", mathQuestion.getId(), e.getMessage(), e);
                        return Mono.just("生题失败: " + e.getMessage());
                    })
                    .block();
            log.info(result);
        } catch (Exception e) {
            log.error("调用dify生题失败: questionId={}, 错误={}", mathQuestion.getId(), e.getMessage(), e);
        }
    }

    ThreadPoolExecutor highschoolExecutor = new ThreadPoolExecutor(
            corePoolSize,
            maximumPoolSize,
            keepAliveTime,
            TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<>(5000), // 配置的默认5000，题数据量积压不要超过5000可能报错
            new ThreadPoolExecutor.AbortPolicy());

    /**
     * 高中讲义生题
     *
     * @param pageKnowledgePointParam
     */
    public void generateQuestionHandoutsHighschool(PageKnowledgePointParam pageKnowledgePointParam) {
        log.info("高中讲义生题开始");
        Page<MathKnowledgePointVO> pageParam = new Page<>(pageKnowledgePointParam.getPage(),
                pageKnowledgePointParam.getSize());
        Page<MathKnowledgePointVO> resultPage = mathKnowledgePointsService.page(pageParam, pageKnowledgePointParam);
        // 获取resultPage中的records数组
        List<MathKnowledgePointVO> list = resultPage.getRecords();
        // 遍历list，调用generate方法
        list.forEach( knowledgePointVO -> highschoolExecutor.execute(() -> generateHandoutsHighschool(knowledgePointVO)));
        log.info("高中讲义异步生题，数量：{}", list.size());
    }

    /**
     * 高中讲义生题
     *
     * @param knowledgePointVO
     */
    private void generateHandoutsHighschool(MathKnowledgePointVO knowledgePointVO) {
        if (StringUtils.isBlank(knowledgePointVO.getHandout())) {
            log.error("高中讲义生题失败,讲义是空: knowledgePointId={}", knowledgePointVO.getId());
            return;
        }
        log.info("高中讲义生题线程操作,线程名: {}",Thread.currentThread().getName());
        DifyHighSchoolGenerateQuestionParam generateQuestionParam = DifyHighSchoolGenerateQuestionParam.builder()
                .knowledgePointId(knowledgePointVO.getId().toString())
                .handout(knowledgePointVO.getHandout())
                .build();
        // 快速判断讲义里面是否有图片
        if (StringUtils.contains(knowledgePointVO.getHandout(), "<img")) {
            // 获取讲义图片链接
            List<ImageData> imageDataList = ImageTagExtractor.extractImageData(knowledgePointVO.getHandout());
            if (CollUtil.isNotEmpty(imageDataList)) {
                List<DifyImageParam> imageList = new ArrayList<>();
                for (ImageData imageData : imageDataList) {
                    String newSrc = filesService.getOssUrl(imageData.getDataS3key(), imageData.getDataS3Enum());
                    if (StringUtils.isBlank(newSrc)) {
                        log.error("高中讲义生题,图片链接为空: knowledgePointId={}", knowledgePointVO.getId());
                        continue;
                    }
                    DifyImageParam difyImageParam = DifyImageParam.builder()
                            .transferMethod("remote_url")
                            .type("image")
                            .url(newSrc)
                            .build();
                    imageList.add(difyImageParam);
                }
                generateQuestionParam.setHandoutImage(imageList);
            }
        }

        DifyParam difyParam = DifyParam.builder()
                .inputs(generateQuestionParam)
                .responseMode("streaming")
                .user("edu-knowledge-manage")
                .build();
        try {
            log.info("高中讲义生题开始请求");
            String result = difyWebClient.post()
                    .uri("/workflows/run")
                    .header(Header.AUTHORIZATION.getValue(), "Bearer " + highSchoolApiKey)
                    .bodyValue(difyParam)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(1200))
                    .onErrorResume(e -> {
                        log.error("调用dify高中讲义生题失败: knowledgePointId={}, 错误={}", knowledgePointVO.getId(),
                                e.getMessage(), e);
                        return Mono.just("高中讲义生题失败: " + e.getMessage());
                    })
                    .block();
                    log.info("请求dify高中讲义生题参数id: {},请求已经发送结果。", knowledgePointVO.getId().toString());
        } catch (Exception e) {
            log.error("调用dify高中讲义生题失败: knowledgePointId={}, 错误={}", knowledgePointVO.getId(), e.getMessage(), e);
        }
    }

    /**
     * 高中题生题
     * @param pageQuestionParam
     * @return
     */
    public void questionToQuestionsHighschool(PageQuestionParam pageQuestionParam) {
        log.info("高中题生题开始");
        Page<MathQuestionVO> pageParam = new Page<>(pageQuestionParam.getPage(), pageQuestionParam.getSize());
        Page<MathQuestionVO> resultPage = mathQuestionsService.page(pageParam, pageQuestionParam);
        // 获取resultPage中的records数组
        List<MathQuestionVO> list = resultPage.getRecords();
        //只获取questionTypes数组不为空的且数组有值的题目
        list = list.stream().filter(questionVO -> questionVO.getQuestionTypes() != null && questionVO.getQuestionTypes().size() > 0).collect(Collectors.toList());
        // 遍历list，添加答案和解析
        list.forEach( questionVO -> {
            //查询答案
            List<MathAnswerVO> answers = mathQuestionsService.listAnswersByQuestionId(questionVO.getId());
            questionVO.setAnswers(answers);
        });
        //调用generate方法
        list.forEach(questionVO -> highschoolExecutor.execute(() -> generateQToQHighschool(questionVO)));
        log.info("高中题异步生题，数量：{},{}", list.size(),list);
    }

    /**
     * 高中题生题
     * @param questionVO
     * @return
     */
    private void generateQToQHighschool(MathQuestionVO questionVO) {
         if (StringUtils.isBlank(questionVO.getContent())) {
            log.error("高中题生题失败,题目是空: questionId={}", questionVO.getId());
            return;
        }
        log.info("高中题生题线程操作,线程名: {}",Thread.currentThread().getName());
        List<MathAnswerVO> answers = questionVO.getAnswers();
        String answer = answers.get(0).getAnswer();
        String analysis = answers.get(0).getContent();
        DifyHighSchoolQToQuestionParam generateQuestionParam = DifyHighSchoolQToQuestionParam.builder()
                .questionTypeId(questionVO.getQuestionTypes().get(0).getId().toString())
                .questionId(questionVO.getId().toString())
                .questionContent(questionVO.getContent())
                .answer(answer)
                .analysis(analysis)
                .fullPath(questionVO.getQuestionTypes().get(0).getFullPath())
                .questionTitle(questionVO.getQuestionTypes().get(0).getName())
                .build();
        // 题目答案、解析中有图片但是1小时过期，在此重新生图片链接
        List<DifyImageParam> imageList = new ArrayList<>();
        getImage(questionVO.getContent(), imageList);
        getImage(answer, imageList);
        getImage(analysis, imageList);
        if (imageList.size()>0) {
            generateQuestionParam.setQuestionImage(imageList);
        }
        DifyParam difyParam = DifyParam.builder()
                .inputs(generateQuestionParam)
                .responseMode("streaming")
                .user("edu-knowledge-manage")
                .build();
        try {
            log.info("高中题生题开始请求,参数:{}",difyParam);
            String result = difyWebClient.post()
                    .uri("/workflows/run")
                    .header(Header.AUTHORIZATION.getValue(), "Bearer " + highSchoolQToQApiKey)
                    .bodyValue(difyParam)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(1200))
                    .onErrorResume(e -> {
                        log.error("调用dify高中题生题失败: questionId={}, 错误={}", questionVO.getId(),
                                e.getMessage(), e);
                        return Mono.just("高中题生题失败: " + e.getMessage());
                    })
                    .block();
                    log.info("请求dify高中题生题参数id: {},dify已经完成得到结果。", questionVO.getId().toString());
        } catch (Exception e) {
            log.error("调用dify高中题生题失败: questionId={}, 错误={}", questionVO.getId(), e.getMessage(), e);
        }
     }

    /**
     * 传入内容获取结果
     * @param content
     * @param imageList
     */
    private void getImage(String content,List<DifyImageParam> imageList) {
        if (StringUtils.contains(content, "<img")) {
            List<ImageData> imageDataList = ImageTagExtractor.extractImageData(content);
            // 获取题目图片链接
            if (CollUtil.isNotEmpty(imageDataList)) {
                for (ImageData imageData : imageDataList) {
                    String newSrc = filesService.getOssUrl(imageData.getDataS3key(), imageData.getDataS3Enum());
                    if (StringUtils.isBlank(newSrc)) {
                        log.error("高中题生题,获取图片链接失败为空: content={}", content);
                        continue;
                    }
                    DifyImageParam difyImageParam = DifyImageParam.builder()
                            .transferMethod("remote_url")
                            .type("image")
                            .url(newSrc)
                            .build();
                    imageList.add(difyImageParam);
                }
            }
        }
    }

    public void generatePrimarySchoolMathQuestionByQuestionTypes(int count) {
        List<AIGeneratedQuestionCount> baseQuestions = mathQuestionsMapper.listPrimarySchoolBaseQuestionByQuestionTypes(count);
        baseQuestions.forEach(questionCount -> executor.execute(() -> generatePrimarySchoolMathQuestionByQuestionTypes(questionCount, count)));
    }

    private void generatePrimarySchoolMathQuestionByQuestionTypes(AIGeneratedQuestionCount aiGeneratedQuestionCount, int limitCount) {
        int generateCount = Math.min(limitCount - aiGeneratedQuestionCount.getTotalCount(), 15);

        MathQuestion mathQuestion = mathQuestionsMapper.selectById(aiGeneratedQuestionCount.getQuestionId());
        QuestionAnswerRelation questionAnswerRelation = questionAnswerRelationsService.lambdaQuery()
                .eq(QuestionAnswerRelation::getQuestionId, aiGeneratedQuestionCount.getQuestionId())
                .list()
                .getFirst();
        QuestionAnswerDetailVO answerVO = mathAnswersService.getById(questionAnswerRelation.getAnswerId());
        DifyGenerateQuestionParam generateQuestionParam = DifyGenerateQuestionParam.builder()
                .questionId(aiGeneratedQuestionCount.getQuestionId().toString())
                .content(mathQuestion.getContent())
                .type(mathQuestion.getQuestionType() == null ? "" : mathQuestion.getQuestionType().getType())
                .answer(answerVO.getAnswer())
                .analysis(answerVO.getContent())
                .numberOfQuestions(String.valueOf(generateCount))
                .questionTypeName(aiGeneratedQuestionCount.getQuestionTypeName())
                .questionTypeId(aiGeneratedQuestionCount.getQuestionTypeId().toString())
                .build();
        QuestionDetailVO questionDetailVO = mathQuestionsService.getDetailById(aiGeneratedQuestionCount.getQuestionId());
        List<DifyImageParam> imageList = new ArrayList<>();
        List<FileVO> fileVOList = questionDetailVO.getFiles();
        if (CollUtil.isNotEmpty(fileVOList)) {
            for (FileVO fileVO : fileVOList) {
                DifyImageParam difyImageParam = DifyImageParam.builder()
                        .transferMethod("remote_url")
                        .type("image")
                        .url(fileVO.getOssUrl())
                        .build();
                imageList.add(difyImageParam);
            }
            generateQuestionParam.setContentImage(imageList);
        }
        DifyParam difyParam = DifyParam.builder()
                .inputs(generateQuestionParam)
                .responseMode("streaming")
                .user("edu-knowledge-manage")
                .build();
        try {
            String result = difyWebClient.post()
                    .uri("/workflows/run")
                    .header(Header.AUTHORIZATION.getValue(),"Bearer " + primaryApiKeyByQuestionTypes)
                    .bodyValue(difyParam)
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofSeconds(1200))
                    .onErrorResume(e -> {
                        log.error("调用dify生题失败: questionId={}, 错误={}", mathQuestion.getId(), e.getMessage(), e);
                        return Mono.just("生题失败: " + e.getMessage());
                    })
                    .block();
            log.info(result);
        } catch (Exception e) {
            log.error("调用dify生题失败: questionId={}, 错误={}", mathQuestion.getId(), e.getMessage(), e);
        }
    }
}
