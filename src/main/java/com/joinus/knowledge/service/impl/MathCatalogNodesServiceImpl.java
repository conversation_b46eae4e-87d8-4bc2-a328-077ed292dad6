package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.config.base.BusinessException;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.mapper.*;
import com.joinus.knowledge.model.entity.*;
import com.joinus.knowledge.model.param.*;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.mapper.MathCatalogNodesMapper;
import com.joinus.knowledge.model.entity.MathCatalogNodes;
import com.joinus.knowledge.model.vo.MathCatalogNodeVO;
import com.joinus.knowledge.model.vo.MathChapterVO;
import com.joinus.knowledge.model.vo.MathSectionVO;
import com.joinus.knowledge.model.vo.SectionKeypointVO;
import com.joinus.knowledge.model.vo.SectionVO;
import com.joinus.knowledge.service.*;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import com.joinus.knowledge.model.vo.SimpleTreeVO;
import com.joinus.knowledge.service.MathCatalogNodesService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【math_catalog_nodes(数学目录节点表)】的数据库操作Service实现
* @createDate 2025-07-30 17:33:40
*/
@Service
public class MathCatalogNodesServiceImpl extends ServiceImpl<MathCatalogNodesMapper, MathCatalogNodes>
    implements MathCatalogNodesService{

    @Resource
    private MathKnowledgePointsMapper mathKnowledgePointsMapper;
    @Resource
    private SectionKnowledgePointsMapper sectionKnowledgePointsMapper;
    @Resource
    private SectionQuestionTypesMapper sectionQuestionTypesMapper;
    @Resource
    private MathQuestionTypesMapper mathQuestionTypesMapper;
    @Resource
    private QuestionKnowledgePointsService questionKnowledgePointsService;
    @Resource
    private QuestionTypesMappingService questionTypesMappingService;;
    @Resource
    private SectionKnowledgePointsService sectionKnowledgePointsService;
    @Resource
    private SectionQuestionTypesService sectionQuestionTypesService;
    @Resource
    private KnowledgePointQuestionTypeRelationshipMapper kpQtRelationMapper;
    @Resource
    private QuestionKnowledgePointsMapper questionKnowledgePointsMapper;
    @Resource
    private QuestionTypesMappingMapper questionTypesMappingMapper;

    @Override
    public List<MathCatalogNodeVO> list(PublisherType publisher, Integer grade, Integer semester) {
        return baseMapper.list(publisher, grade, semester);
    }

    @Override
    public List<MathCatalogNodes> listAllChaptersByBookId(UUID textbookId) {
        // 查询level=1且textbook_id不为空的节点（章节）
        LambdaQueryWrapper<MathCatalogNodes> wrapper = Wrappers.lambdaQuery(MathCatalogNodes.class)
                .eq(MathCatalogNodes::getTextbookId, textbookId)
                .eq(MathCatalogNodes::getLevel, 1)
                .isNotNull(MathCatalogNodes::getTextbookId)
                .orderByAsc(MathCatalogNodes::getSortNo);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public List<MathChapterVO> listChapters(String name, Integer grade, Integer semester, PublisherType publisher) {
        return baseMapper.listChapters(name, grade, semester, publisher);
    }

    @Override
    public List<SectionVO> listAllSectionsByBookId(UUID bookId) {
        return baseMapper.listAllSectionsByBookId(bookId);
    }

    @Override
    public List<MathSectionVO> listSections(String name, Integer grade, Integer semester, PublisherType publisher, String chapterName, UUID chapterId) {
        return baseMapper.listSections(name, grade, semester, publisher, chapterName, chapterId);
    }

    @Override
    public List<MathCatalogNodes> getByPageNo(Integer pageNo, UUID textbookId, UUID sectionId) {
        return baseMapper.getByPageNo(pageNo, textbookId, sectionId);
    }

    @Override
    public List<SectionKeypointVO> listKeypointsById(UUID sectionId) {
        return baseMapper.listKeypointsById(sectionId);
    }

    @Override
    public List<SectionVO> listAllSectionsByPublisher(PublisherType publisher) {
        return baseMapper.listAllSectionsByPublisher(publisher);
    }

    @Override
    public List<SectionVO> listSectionsByCondition(Integer grade, PublisherType publisher, Integer semester) {
        return baseMapper.listSectionsByCondition(grade, publisher, semester);
    }

    @Override
    public List<MathCatalogNodes> listByTextbookId(UUID textbookId) {
        return baseMapper.listByTextbookId(textbookId);
    }

    @Override
    public void updateSectionKeypointMapping(UpdateSectionKeypointParam param) {
        MathCatalogNodes mathSection = getById(param.getSectionId());
        if (null == mathSection) {
            throw new BusinessException("要关联小节不存在");
        }

        if (param.getType().equals("knowledgePoint") || param.getType().equals("examPoint")) {

            MathKnowledgePoint mathKnowledgePoint = mathKnowledgePointsMapper.selectById(param.getKeyPointId());
            if (null == mathKnowledgePoint) {
                throw new BusinessException("关联知识点不存在");
            }

            LambdaQueryWrapper<SectionKnowledgePoint> oldEq = Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                    .eq(SectionKnowledgePoint::getSectionId, param.getOldSectionId())
                    .eq(SectionKnowledgePoint::getKnowledgePointId, param.getKeyPointId());
            List<SectionKnowledgePoint> existOldMappings = sectionKnowledgePointsMapper.selectList(oldEq);
            if (CollUtil.isEmpty(existOldMappings)) {
                throw new BusinessException("原关联关系不存在");
            }

            LambdaQueryWrapper<SectionKnowledgePoint> newEq = Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                    .eq(SectionKnowledgePoint::getSectionId, param.getSectionId())
                    .eq(SectionKnowledgePoint::getKnowledgePointId, param.getKeyPointId());
            List<SectionKnowledgePoint> existNewMappings = sectionKnowledgePointsMapper.selectList(newEq);
            if (CollUtil.isNotEmpty(existNewMappings)) {
                throw new BusinessException("新关系已存在");
            }

            sectionKnowledgePointsMapper.delete(oldEq);
            SectionKnowledgePoint newMapping = SectionKnowledgePoint.builder()
                    .sectionId(param.getSectionId())
                    .knowledgePointId(param.getKeyPointId())
                    .pageIndex(existOldMappings.get(0).getPageIndex())
                    .build();
            sectionKnowledgePointsMapper.insert(newMapping);
        } else {

            MathQuestionType mathQuestionType = mathQuestionTypesMapper.selectById(param.getKeyPointId());
            if (null == mathQuestionType) {
                throw new BusinessException("关联题型不存在");
            }

            LambdaQueryWrapper<SectionQuestionType> oldEq = Wrappers.lambdaQuery(SectionQuestionType.class)
                    .eq(SectionQuestionType::getSectionId, param.getOldSectionId())
                    .eq(SectionQuestionType::getQuestionTypeId, param.getKeyPointId());
            List<SectionQuestionType> existOldMappings = sectionQuestionTypesMapper.selectList(oldEq);
            if (CollUtil.isEmpty(existOldMappings)) {
                throw new BusinessException("原关联关系不存在");
            }

            LambdaQueryWrapper<SectionQuestionType> newEq = Wrappers.lambdaQuery(SectionQuestionType.class)
                    .eq(SectionQuestionType::getSectionId, param.getSectionId())
                    .eq(SectionQuestionType::getQuestionTypeId, param.getKeyPointId());
            List<SectionQuestionType> existNewMappings = sectionQuestionTypesMapper.selectList(newEq);
            if (CollUtil.isNotEmpty(existNewMappings)) {
                throw new BusinessException("新关系已存在");
            }

            sectionQuestionTypesMapper.delete(oldEq);
            SectionQuestionType newMapping = SectionQuestionType.builder()
                    .sectionId(param.getSectionId())
                    .questionTypeId(param.getKeyPointId())
                    .pageIndex(existOldMappings.get(0).getPageIndex())
                    .build();
            sectionQuestionTypesMapper.insert(newMapping);
        }
    }

    @Override
    public void deleteSectionKeypointMapping(DeleteSectionKeypointParam param) {
        MathCatalogNodes mathSection = getById(param.getSectionId());
        if (null == mathSection) {
            throw new BusinessException("要删除关系的小节不存在");
        }

        if (param.getType().equals("knowledgePoint") || param.getType().equals("examPoint")) {

            MathKnowledgePoint mathKnowledgePoint = mathKnowledgePointsMapper.selectById(param.getKeyPointId());
            if (null == mathKnowledgePoint) {
                throw new BusinessException("要删除关系关联知识点不存在");
            }

            LambdaQueryWrapper<SectionKnowledgePoint> oldEq = Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                    .eq(SectionKnowledgePoint::getSectionId, param.getSectionId())
                    .eq(SectionKnowledgePoint::getKnowledgePointId, param.getKeyPointId());
            List<SectionKnowledgePoint> existOldMappings = sectionKnowledgePointsMapper.selectList(oldEq);
            if (CollUtil.isEmpty(existOldMappings)) {
                throw new BusinessException("要删除关系不存在");
            }
            sectionKnowledgePointsMapper.delete(oldEq);
        } else {

            MathQuestionType mathQuestionType = mathQuestionTypesMapper.selectById(param.getKeyPointId());
            if (null == mathQuestionType) {
                throw new BusinessException("要删除关系关联题型不存在");
            }

            LambdaQueryWrapper<SectionQuestionType> oldEq = Wrappers.lambdaQuery(SectionQuestionType.class)
                    .eq(SectionQuestionType::getSectionId, param.getSectionId())
                    .eq(SectionQuestionType::getQuestionTypeId, param.getKeyPointId());
            List<SectionQuestionType> existOldMappings = sectionQuestionTypesMapper.selectList(oldEq);
            if (CollUtil.isEmpty(existOldMappings)) {
                throw new BusinessException("要删除关系不存在");
            }
            sectionQuestionTypesMapper.delete(oldEq);
        }
    }

    @Override
    public void switchKeypointType(SwitchKeypointTypeParam param) {
        if ((param.getType().equals("knowledgePoint") || param.getType().equals("examPoint"))
                && param.getTargetType().equals("questionType")) {
            MathKnowledgePoint mathKnowledgePoint = mathKnowledgePointsMapper.selectById(param.getKeyPointId());
            if (null == mathKnowledgePoint) {
                throw new BusinessException("要切换知识点不存在");
            }
            MathQuestionType existMathQuestionType = mathQuestionTypesMapper.selectById(param.getKeyPointId());
            if (null != existMathQuestionType) {
                throw new BusinessException("要切换题型已存在");
            }

            MathQuestionType mathQuestionType = MathQuestionType.builder()
                    .id(mathKnowledgePoint.getId())
                    .name(mathKnowledgePoint.getName())
                    .sortNo(mathKnowledgePoint.getSortNo())
                    .build();
            mathQuestionTypesMapper.insert(mathQuestionType);

            //处理知识点与题目的关系
            List<UUID> questionIds = questionKnowledgePointsService.listQuestionIdsByKnowledgePointId(param.getKeyPointId());
            if (CollUtil.isNotEmpty(questionIds)) {
                List<QuestionTypesMapping> questionTypesMappings = questionIds.stream()
                        .filter(questionId -> {
                            List<QuestionTypesMapping> existQtms = questionTypesMappingMapper.selectList(
                                    Wrappers.lambdaQuery(QuestionTypesMapping.class)
                                            .eq(QuestionTypesMapping::getQuestionId, questionId)
                                            .eq(QuestionTypesMapping::getQuestionTypeId, mathKnowledgePoint.getId())
                            );
                            return CollUtil.isEmpty(existQtms);
                        }).map(questionId -> {
                            return QuestionTypesMapping.builder()
                                    .questionId(questionId)
                                    .questionTypeId(mathKnowledgePoint.getId())
                                    .build();
                        }).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(questionTypesMappings)) {
                    questionTypesMappingService.saveBatch(questionTypesMappings);
                }
            }

            //处理知识点与小节的关系
            LambdaQueryWrapper<SectionKnowledgePoint> eq = Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                    .eq(SectionKnowledgePoint::getKnowledgePointId, param.getKeyPointId());
            List<SectionKnowledgePoint> sectionKnowledgePoints = sectionKnowledgePointsMapper.selectList(eq);
            sectionKnowledgePoints.forEach(sectionKnowledgePoint -> {
                List<SectionQuestionType> sectionQuestionTypes = sectionQuestionTypesMapper.selectList(Wrappers.lambdaQuery(SectionQuestionType.class)
                        .eq(SectionQuestionType::getSectionId, sectionKnowledgePoint.getSectionId())
                        .eq(SectionQuestionType::getQuestionTypeId, sectionKnowledgePoint.getKnowledgePointId()));
                if (CollUtil.isEmpty(sectionQuestionTypes)) {
                    SectionQuestionType sectionQuestionType = SectionQuestionType.builder()
                            .sectionId(sectionKnowledgePoint.getSectionId())
                            .questionTypeId(sectionKnowledgePoint.getKnowledgePointId())
                            .pageIndex(sectionKnowledgePoint.getPageIndex())
                            .build();
                    sectionQuestionTypesMapper.insert(sectionQuestionType);
                }
            });

            mathKnowledgePointsMapper.realDeleteById(mathKnowledgePoint.getId());
            questionKnowledgePointsService.deleteAssociationsByKnowledgePointId(param.getKeyPointId());
            sectionKnowledgePointsMapper.delete(eq);
            kpQtRelationMapper.delete(Wrappers.lambdaQuery(KnowledgePointQuestionTypeRelationship.class)
                    .eq(KnowledgePointQuestionTypeRelationship::getKnowledgePointId, param.getKeyPointId()));

        } else if (param.getType().equals("questionType")
                && (param.getTargetType().equals("knowledgePoint") || param.getTargetType().equals("examPoint"))){
            MathQuestionType mathQuestionType = mathQuestionTypesMapper.selectById(param.getKeyPointId());

            if (null == mathQuestionType) {
                throw new BusinessException("要切换题型不存在");
            }
            MathKnowledgePoint existMathKnowledgePoint = mathKnowledgePointsMapper.selectById(param.getKeyPointId());
            if (null != existMathKnowledgePoint) {
                throw new BusinessException("要切知识点已存在");
            }

            MathKnowledgePoint mathKnowledgePoint = MathKnowledgePoint.builder()
                    .id(mathQuestionType.getId())
                    .name(mathQuestionType.getName())
                    .examPoint(param.getTargetType().equals("examPoint") ? true : false)
                    .sortNo(mathQuestionType.getSortNo())
                    .build();
            mathKnowledgePointsMapper.insert(mathKnowledgePoint);

            List<UUID> questionIds = questionTypesMappingService.listQuestionIdsByQuestionTypeId(param.getKeyPointId());
            if (CollUtil.isNotEmpty(questionIds)) {
                List<QuestionKnowledgePoint> questionKnowledgePoints = questionIds.stream()
                        .filter(questionId -> {
                            List<QuestionKnowledgePoint> existQks = questionKnowledgePointsMapper.selectList(
                                    Wrappers.lambdaQuery(QuestionKnowledgePoint.class)
                                            .eq(QuestionKnowledgePoint::getQuestionId, questionId)
                                            .eq(QuestionKnowledgePoint::getKnowledgePointId, mathQuestionType.getId())
                            );
                            return CollUtil.isEmpty(existQks);
                        }).map(questionId -> {
                            return QuestionKnowledgePoint.builder()
                                    .questionId(questionId)
                                    .knowledgePointId(mathQuestionType.getId())
                                    .build();
                        }).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(questionKnowledgePoints)) {
                    questionKnowledgePointsService.saveBatch(questionKnowledgePoints);
                }
            }

            LambdaQueryWrapper<SectionQuestionType> eq = Wrappers.lambdaQuery(SectionQuestionType.class)
                    .eq(SectionQuestionType::getQuestionTypeId, param.getKeyPointId());
            List<SectionQuestionType> sectionQuestionTypes = sectionQuestionTypesMapper.selectList(eq);
            sectionQuestionTypes.forEach(sectionQuestionType -> {
                List<SectionKnowledgePoint> sectionKnowledgePoints = sectionKnowledgePointsMapper.selectList(Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                        .eq(SectionKnowledgePoint::getSectionId, sectionQuestionType.getSectionId())
                        .eq(SectionKnowledgePoint::getKnowledgePointId, param.getKeyPointId()));
                if (CollUtil.isEmpty(sectionKnowledgePoints)) {
                    SectionKnowledgePoint sectionKnowledgePoint = SectionKnowledgePoint.builder()
                            .sectionId(sectionQuestionType.getSectionId())
                            .knowledgePointId(sectionQuestionType.getQuestionTypeId())
                            .pageIndex(sectionQuestionType.getPageIndex())
                            .build();
                    sectionKnowledgePointsMapper.insert(sectionKnowledgePoint);
                }
            });

            mathQuestionTypesMapper.realDeleteById(mathQuestionType.getId());
            questionTypesMappingService.deleteAssociationsByQuestionTypeId(param.getKeyPointId());
            sectionQuestionTypesMapper.delete(eq);
            kpQtRelationMapper.delete(Wrappers.lambdaQuery(KnowledgePointQuestionTypeRelationship.class)
                    .eq(KnowledgePointQuestionTypeRelationship::getQuestionTypeId, param.getKeyPointId()));
        } else {
            boolean examPoint = param.getTargetType().equals("examPoint") ? true : false;
            mathKnowledgePointsMapper.updateById(MathKnowledgePoint.builder().id(param.getKeyPointId()).examPoint(examPoint).build());
        }
    }

    @Override
    public void updatePageIndex(UpdateSectionKeypointParam param) {
        if (param.getType().equals("questionType")) {
            LambdaQueryWrapper<SectionQuestionType> queryWrapper = Wrappers.lambdaQuery(SectionQuestionType.class)
                    .eq(SectionQuestionType::getSectionId, param.getSectionId())
                    .eq(SectionQuestionType::getQuestionTypeId, param.getKeyPointId())
                    .eq(null != param.getOldPageIndex(), SectionQuestionType::getPageIndex, param.getOldPageIndex())
                    .isNull(null == param.getOldPageIndex(), SectionQuestionType::getPageIndex);
            List<SectionQuestionType> sectionQuestionTypes = sectionQuestionTypesMapper.selectList(queryWrapper);
            if (CollUtil.isEmpty(sectionQuestionTypes)) {
                throw new BusinessException("未找到该小节题型关系");
            }
            LambdaUpdateWrapper<SectionQuestionType> wrapper = Wrappers.lambdaUpdate(SectionQuestionType.class)
                    .eq(SectionQuestionType::getSectionId, param.getSectionId())
                    .eq(SectionQuestionType::getQuestionTypeId, param.getKeyPointId())
                    .eq(null != param.getOldPageIndex(), SectionQuestionType::getPageIndex, param.getOldPageIndex())
                    .isNull(null == param.getOldPageIndex(), SectionQuestionType::getPageIndex)
                    .set(SectionQuestionType::getPageIndex, param.getPageIndex());
            sectionQuestionTypesMapper.update(wrapper);
        } else if (param.getType().equals("knowledgePoint") || param.getType().equals("examPoint")) {
            LambdaQueryWrapper<SectionKnowledgePoint> queryWrapper = Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                    .eq(SectionKnowledgePoint::getSectionId, param.getSectionId())
                    .eq(SectionKnowledgePoint::getKnowledgePointId, param.getKeyPointId())
                    .eq(null != param.getOldPageIndex(), SectionKnowledgePoint::getKnowledgePointId, param.getOldPageIndex())
                    .isNull(null == param.getOldPageIndex(), SectionKnowledgePoint::getPageIndex);
            List<SectionKnowledgePoint> sectionKnowledgePoints = sectionKnowledgePointsMapper.selectList(queryWrapper);
            if (CollUtil.isEmpty(sectionKnowledgePoints)) {
                throw new BusinessException("未找到该小节知识点关系");
            }

            LambdaUpdateWrapper<SectionKnowledgePoint> wrapper = Wrappers.lambdaUpdate(SectionKnowledgePoint.class)
                    .eq(SectionKnowledgePoint::getSectionId, param.getSectionId())
                    .eq(SectionKnowledgePoint::getKnowledgePointId, param.getKeyPointId())
                    .eq(null != param.getOldPageIndex(), SectionKnowledgePoint::getKnowledgePointId, param.getOldPageIndex())
                    .isNull(null == param.getOldPageIndex(), SectionKnowledgePoint::getPageIndex)
                    .set(SectionKnowledgePoint::getPageIndex, param.getPageIndex());
            sectionKnowledgePointsMapper.update(wrapper);
        }
    }

    @Override
    public UUID combineKeypoints(CombineKeypointParam param) {
        if (param.getType().equals("knowledgePoint")) {
            LambdaQueryWrapper<MathKnowledgePoint> eq1 = Wrappers.lambdaQuery(MathKnowledgePoint.class)
                    .eq(MathKnowledgePoint::getId, param.getKeyPointId1());
            MathKnowledgePoint mathKnowledgePoint1 = mathKnowledgePointsMapper.selectOne(eq1);
            if (null == mathKnowledgePoint1) {
                throw new BusinessException("要合并知识点1不存在");
            }
            LambdaQueryWrapper<MathKnowledgePoint> eq2 = Wrappers.lambdaQuery(MathKnowledgePoint.class)
                    .eq(MathKnowledgePoint::getId, param.getKeyPointId2());
            MathKnowledgePoint mathKnowledgePoint2 = mathKnowledgePointsMapper.selectOne(eq2);
            if (null == mathKnowledgePoint2) {
                throw new BusinessException("要合并知识点2不存在");
            }
            if (!mathKnowledgePoint1.getName().equals(mathKnowledgePoint2.getName())) {
                throw new BusinessException("要合并知识点1与知识点2名称不一致");
            }
            UUID remainId = getRemainKnowledgePointId(mathKnowledgePoint1, mathKnowledgePoint2);
            UUID removeId = param.getKeyPointId1().equals(remainId) ? param.getKeyPointId2() : param.getKeyPointId1();


            //修改知识点与题目的关系
            LambdaQueryWrapper<QuestionKnowledgePoint> questionKpWrapper = Wrappers.lambdaQuery(QuestionKnowledgePoint.class)
                    .eq(QuestionKnowledgePoint::getKnowledgePointId, removeId);
            List<QuestionKnowledgePoint> questionKnowledgePoints = questionKnowledgePointsMapper.selectList(questionKpWrapper);

            if (CollUtil.isNotEmpty(questionKnowledgePoints)) {
                questionKnowledgePoints.stream().forEach(questionKp -> {
                    List<QuestionKnowledgePoint> existRemainQuestionKnowledgePoints = questionKnowledgePointsMapper.selectList(Wrappers.lambdaQuery(QuestionKnowledgePoint.class)
                            .eq(QuestionKnowledgePoint::getQuestionId, questionKp.getQuestionId())
                            .eq(QuestionKnowledgePoint::getKnowledgePointId, remainId));
                    if (CollUtil.isEmpty(existRemainQuestionKnowledgePoints)) {
                        questionKnowledgePointsMapper.update(QuestionKnowledgePoint.builder()
                                .knowledgePointId(remainId)
                                .build(), questionKpWrapper.eq(QuestionKnowledgePoint::getQuestionId, questionKp.getQuestionId()));
                    } else {
                        questionKnowledgePointsMapper.delete(questionKpWrapper.eq(QuestionKnowledgePoint::getQuestionId, questionKp.getQuestionId()));
                    }
                });
            }

            //修改知识点与小节的关系
            LambdaQueryWrapper<SectionKnowledgePoint> sectionKpWrapper = Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                    .eq(SectionKnowledgePoint::getKnowledgePointId, removeId);
            List<SectionKnowledgePoint> sectionKnowledgePoints = sectionKnowledgePointsMapper.selectList(sectionKpWrapper);

            sectionKnowledgePoints.forEach(sectionKp -> {
                List<SectionKnowledgePoint> existRemainSectionKnowledgePoints = sectionKnowledgePointsMapper.selectList(Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                        .eq(SectionKnowledgePoint::getSectionId, sectionKp.getSectionId())
                        .eq(SectionKnowledgePoint::getKnowledgePointId, remainId));
                if (CollUtil.isEmpty(existRemainSectionKnowledgePoints)) {
                    sectionKnowledgePointsMapper.update(SectionKnowledgePoint.builder()
                            .knowledgePointId(remainId)
                            .build(), sectionKpWrapper.eq(SectionKnowledgePoint::getSectionId, sectionKp.getSectionId()));
                } else {
                    sectionKnowledgePointsMapper.delete(sectionKpWrapper.eq(SectionKnowledgePoint::getSectionId, sectionKp.getSectionId())
                    );
                }
            });

            //修改知识点与题型的关系
            LambdaQueryWrapper<KnowledgePointQuestionTypeRelationship> kpQtWrapper = Wrappers.lambdaQuery(KnowledgePointQuestionTypeRelationship.class)
                    .eq(KnowledgePointQuestionTypeRelationship::getKnowledgePointId, removeId);
            List<KnowledgePointQuestionTypeRelationship> kpQtRelationships = kpQtRelationMapper.selectList(kpQtWrapper);
            kpQtRelationships.forEach(kpQtRelationship -> {
                List<KnowledgePointQuestionTypeRelationship> existRemainKpQtRelationship = kpQtRelationMapper.selectList(Wrappers.lambdaQuery(KnowledgePointQuestionTypeRelationship.class)
                        .eq(KnowledgePointQuestionTypeRelationship::getQuestionTypeId, kpQtRelationship.getQuestionTypeId())
                        .eq(KnowledgePointQuestionTypeRelationship::getKnowledgePointId, remainId));
                if (CollUtil.isEmpty(existRemainKpQtRelationship)) {
                    kpQtRelationMapper.update(KnowledgePointQuestionTypeRelationship.builder()
                            .knowledgePointId(remainId)
                            .build(), kpQtWrapper.eq(KnowledgePointQuestionTypeRelationship::getQuestionTypeId, kpQtRelationship.getQuestionTypeId()));
                } else {
                    kpQtRelationMapper.delete(kpQtWrapper.eq(KnowledgePointQuestionTypeRelationship::getQuestionTypeId, kpQtRelationship.getQuestionTypeId()));
                }
            });

            //删除知识点
            mathKnowledgePointsMapper.realDeleteById(removeId);
            return remainId;
        } else {
            LambdaQueryWrapper<MathQuestionType> eq1 = Wrappers.lambdaQuery(MathQuestionType.class)
                    .eq(MathQuestionType::getId, param.getKeyPointId1());
            MathQuestionType mathQuestionType1 = mathQuestionTypesMapper.selectOne(eq1);
            if (null == mathQuestionType1) {
                throw new BusinessException("要合并题型1不存在");
            }
            LambdaQueryWrapper<MathQuestionType> eq2 = Wrappers.lambdaQuery(MathQuestionType.class)
                    .eq(MathQuestionType::getId, param.getKeyPointId2());
            MathQuestionType mathQuestionType2 = mathQuestionTypesMapper.selectOne(eq2);
            if (null == mathQuestionType2) {
                throw new BusinessException("要合并题型2不存在");
            }
            if (!mathQuestionType1.getName().equals(mathQuestionType2.getName())) {
                throw new BusinessException("要合并题型1与题型2名称不一致");
            }
            UUID remainId = mathQuestionType1.getId();
            UUID removeId = mathQuestionType2.getId();

            //修改题型与题目的关系
            LambdaQueryWrapper<QuestionTypesMapping> qtmWrapper = Wrappers.lambdaQuery(QuestionTypesMapping.class)
                    .eq(QuestionTypesMapping::getQuestionTypeId, removeId);
            List<QuestionTypesMapping> questionTypesMappings = questionTypesMappingMapper.selectList(qtmWrapper);
            if (CollUtil.isNotEmpty(questionTypesMappings)) {
                questionTypesMappings.stream().forEach(questionTypesMapping -> {
                    List<QuestionTypesMapping> existRemainQuestionTypesMappings = questionTypesMappingMapper.selectList(Wrappers.lambdaQuery(QuestionTypesMapping.class)
                            .eq(QuestionTypesMapping::getQuestionId, questionTypesMapping.getQuestionId())
                            .eq(QuestionTypesMapping::getQuestionTypeId, remainId));
                    if (CollUtil.isEmpty(existRemainQuestionTypesMappings)) {
                        questionTypesMappingMapper.update(QuestionTypesMapping.builder().questionTypeId(remainId).build(),
                                qtmWrapper.eq(QuestionTypesMapping::getQuestionId, questionTypesMapping.getQuestionId()));
                    } else {
                        questionTypesMappingMapper.delete(qtmWrapper.eq(QuestionTypesMapping::getQuestionId, questionTypesMapping.getQuestionId()));
                    }
                });
            }

            //修改题型与小节的关系
            LambdaQueryWrapper<SectionQuestionType> sectionQtWrapper = Wrappers.lambdaQuery(SectionQuestionType.class)
                    .eq(SectionQuestionType::getQuestionTypeId, removeId);
            List<SectionQuestionType> sectionKnowledgePoints = sectionQuestionTypesMapper.selectList(sectionQtWrapper);
            sectionKnowledgePoints.forEach(sectionKnowledgePoint -> {
                List<SectionQuestionType> existRemainSectionQts = sectionQuestionTypesMapper.selectList(Wrappers.lambdaQuery(SectionQuestionType.class)
                        .eq(SectionQuestionType::getSectionId, sectionKnowledgePoint.getSectionId())
                        .eq(SectionQuestionType::getQuestionTypeId, remainId));
                if (CollUtil.isEmpty(existRemainSectionQts)) {
                    sectionQuestionTypesMapper.update(SectionQuestionType.builder()
                            .questionTypeId(remainId)
                            .build(), sectionQtWrapper.eq(SectionQuestionType::getSectionId, sectionKnowledgePoint.getSectionId()));
                } else {
                    sectionQuestionTypesMapper.delete(sectionQtWrapper.eq(SectionQuestionType::getSectionId, sectionKnowledgePoint.getSectionId()));
                }
            });

            //修改知识点与题型的关系
            LambdaQueryWrapper<KnowledgePointQuestionTypeRelationship> kpQtWrapper = Wrappers.lambdaQuery(KnowledgePointQuestionTypeRelationship.class)
                    .eq(KnowledgePointQuestionTypeRelationship::getQuestionTypeId, removeId);
            List<KnowledgePointQuestionTypeRelationship> kpQtRelationships = kpQtRelationMapper.selectList(kpQtWrapper);
            kpQtRelationships.forEach(kpQtRelationship -> {
                List<KnowledgePointQuestionTypeRelationship> existRemainKpQtRelationship = kpQtRelationMapper.selectList(Wrappers.lambdaQuery(KnowledgePointQuestionTypeRelationship.class)
                        .eq(KnowledgePointQuestionTypeRelationship::getKnowledgePointId, kpQtRelationship.getKnowledgePointId())
                        .eq(KnowledgePointQuestionTypeRelationship::getQuestionTypeId, remainId));
                if (CollUtil.isEmpty(existRemainKpQtRelationship)) {
                    kpQtRelationMapper.update(KnowledgePointQuestionTypeRelationship.builder()
                            .knowledgePointId(remainId)
                            .build(), kpQtWrapper.eq(KnowledgePointQuestionTypeRelationship::getKnowledgePointId, kpQtRelationship.getKnowledgePointId()));
                } else {
                    kpQtRelationMapper.delete(kpQtWrapper.eq(KnowledgePointQuestionTypeRelationship::getKnowledgePointId, kpQtRelationship.getKnowledgePointId()));
                }
            });

            //删除题型
            mathQuestionTypesMapper.realDeleteById(removeId);
            return remainId;
        }
    }

    private UUID getRemainKnowledgePointId(MathKnowledgePoint mathKnowledgePoint1, MathKnowledgePoint mathKnowledgePoint2) {
        if (null != mathKnowledgePoint1.getExamPoint() && mathKnowledgePoint1.getExamPoint()) {
            return mathKnowledgePoint1.getId();
        }
        if (null != mathKnowledgePoint2.getExamPoint() && mathKnowledgePoint2.getExamPoint()) {
            return mathKnowledgePoint2.getId();
        }
        if (null != mathKnowledgePoint1.getExamPoint()) {
            return mathKnowledgePoint1.getId();
        }
        if (null != mathKnowledgePoint2.getExamPoint()) {
            return mathKnowledgePoint2.getId();
        }
        return mathKnowledgePoint1.getId();
    }

    @Override
    public void addKeypoints(AddSectionKeypointParam param) {
        if (param.getType().equals("knowledgePoint") || param.getType().equals("examPoint")) {
            sectionKnowledgePointsService.createRelation(param.getSectionId(), param.getKeyPointId(), param.getPageIndex());
        } else if (param.getType().equals("questionType")) {
            sectionQuestionTypesService.createRelation(param.getSectionId(), param.getKeyPointId(), param.getPageIndex());
        } else {
            throw new BusinessException("type参数错误");
        }
    }


    @Override
    public List<SimpleTreeVO> tree(PublisherType publisher, Integer grade, Integer semester) {
        List<MathCatalogNodeVO> nodes = baseMapper.list(publisher, grade, semester);
        return buildTreeNonRecursive(nodes);
    }

    /**
     * 非递归方式构建树结构，时间复杂度O(n)
     * 使用HashMap进行快速节点查找和父子关系建立
     *
     * @param nodes 所有节点列表
     * @return 构建好的树结构列表（根节点列表）
     */
    private List<SimpleTreeVO> buildTreeNonRecursive(List<MathCatalogNodeVO> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return new ArrayList<>();
        }

        // 使用Map存储所有节点，key为节点ID，便于快速查找
        Map<UUID, SimpleTreeVO> nodeMap = new HashMap<>();
        List<SimpleTreeVO> rootNodes = new ArrayList<>();

        // 第一次遍历：创建所有节点对象并存入Map
        for (MathCatalogNodeVO node : nodes) {
            SimpleTreeVO treeNode = new SimpleTreeVO();
            treeNode.setId(node.getId());
            treeNode.setName(node.getName());
            treeNode.setParentId(node.getParentId());
            treeNode.setSortNo(node.getSortNo()); // 设置排序号
            treeNode.setChildren(new ArrayList<>());
            nodeMap.put(node.getId(), treeNode);
        }

        // 第二次遍历：建立父子关系
        for (SimpleTreeVO treeNode : nodeMap.values()) {
            UUID parentId = treeNode.getParentId();
            if (parentId == null) {
                // 根节点（parentId为null）
                rootNodes.add(treeNode);
            } else {
                // 子节点，找到父节点并添加到父节点的children中
                SimpleTreeVO parentNode = nodeMap.get(parentId);
                if (parentNode != null) {
                    parentNode.getChildren().add(treeNode);
                }
            }
        }

        // 第三次遍历：对所有节点的children按sortNo排序
        sortChildrenRecursively(rootNodes);

        // 对根节点也按sortNo排序
        compareSortNo(rootNodes);

        return rootNodes;
    }

    private void compareSortNo(List<SimpleTreeVO> rootNodes) {
        rootNodes.sort((a, b) -> {
            Integer sortA = a.getSortNo();
            Integer sortB = b.getSortNo();
            if (sortA == null && sortB == null) return 0;
            if (sortA == null) return 1;
            if (sortB == null) return -1;
            return sortA.compareTo(sortB);
        });
    }

    /**
     * 递归对所有节点的children按sortNo排序
     *
     * @param nodes 需要排序的节点列表
     */
    private void sortChildrenRecursively(List<SimpleTreeVO> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }

        for (SimpleTreeVO node : nodes) {
            List<SimpleTreeVO> children = node.getChildren();
            if (children != null && !children.isEmpty()) {
                // 对当前节点的children按sortNo排序
                compareSortNo(children);

                // 递归排序子节点的children
                sortChildrenRecursively(children);
            }
        }
    }
}




