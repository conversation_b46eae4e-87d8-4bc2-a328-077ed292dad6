package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.config.base.BusinessException;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.mapper.MathHandoutSlideshowPresentationMapper;
import com.joinus.knowledge.model.entity.MathHandoutSlideshowPresentation;
import com.joinus.knowledge.model.param.MathHandoutSlideParam;
import com.joinus.knowledge.model.vo.MathHandoutSlideshowPageVO;
import com.joinus.knowledge.model.vo.MathHandoutSlideshowVO;
import com.joinus.knowledge.service.MathHandoutSlideshowPresentationService;
import com.joinus.knowledge.utils.AliOssUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_handout_slideshow_presentations(讲义幻灯片展示表)】的数据库操作Service实现
* @createDate 2025-08-19 13:49:20
*/
@Slf4j
@Service
public class MathHandoutSlideshowPresentationServiceImpl extends ServiceImpl<MathHandoutSlideshowPresentationMapper, MathHandoutSlideshowPresentation>
    implements MathHandoutSlideshowPresentationService{

    @Autowired
    private AliOssUtils aliOssUtils;

    @Override
    public UUID saveOrUpdate(UUID id, List<MathHandoutSlideParam> slides) {
        MathHandoutSlideshowPresentation existSlideshow = lambdaQuery().eq(MathHandoutSlideshowPresentation::getHandoutId, id)
                .one();
        if (null == existSlideshow) {
            MathHandoutSlideshowPresentation newSlideshow = MathHandoutSlideshowPresentation.builder()
                    .handoutId(id)
                    .pptHtmls(JSONUtil.toJsonStr(slides))
                    .build();
            save(newSlideshow);
            return newSlideshow.getId();
        }
        MathHandoutSlideshowPresentation updateSlideShow = MathHandoutSlideshowPresentation.builder()
                .id(existSlideshow.getId())
                .pptHtmls(JSONUtil.toJsonStr(slides))
                .build();
        updateById(updateSlideShow);
        return updateSlideShow.getId();
    }

    @Override
    public List<MathHandoutSlideshowPageVO> listPptHtmlsBySlideshowId(UUID slideshowId) {
        MathHandoutSlideshowPresentation mathHandoutSlideshowPresentation = baseMapper.selectById(slideshowId);
        if (null == slideshowId) {
            throw new BusinessException("讲义ppt-htmls不存在");
        }
        List<MathHandoutSlideshowPageVO> results = JSONUtil.toList(mathHandoutSlideshowPresentation.getPptHtmls(), MathHandoutSlideshowPageVO.class);
        if (CollUtil.isEmpty(results)) {
            return List.of();
        }
        results.stream().forEach(slideshowPage -> {
            if (CollUtil.isNotEmpty(slideshowPage.getVoices())) {
                slideshowPage.getVoices().stream().forEach(voice -> {
                    voice.setOssUrl(aliOssUtils.generatePresignedUrl(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB.getBucket(), voice.getOssKey()));
                });
            }
        });
        return results;
    }

    @Override
    public List<MathHandoutSlideshowPageVO> updatePptHtmls(UUID slideshowId, List<MathHandoutSlideParam> slides) {
        MathHandoutSlideshowPresentation slideshowPresentation = baseMapper.selectById(slideshowId);
        if (null == slideshowPresentation) {
            throw new BusinessException("讲义ppt-htmls不存在");
        }
        MathHandoutSlideshowPresentation updateSlideshow = MathHandoutSlideshowPresentation
                .builder()
                .id(slideshowPresentation.getId())
                .pptHtmls(JSONUtil.toJsonStr(slides))
                .build();
        updateById(updateSlideshow);
        return listPptHtmlsBySlideshowId(slideshowId);
    }

    @Override
    public List<MathHandoutSlideshowVO> listSlideshowsByHandoutId(UUID id) {
        List<MathHandoutSlideshowPresentation> slideshows = lambdaQuery().eq(MathHandoutSlideshowPresentation::getHandoutId, id)
                .list();
        if (CollUtil.isEmpty(slideshows)) {
            return List.of();
        }
        return slideshows.stream().map(slideshow -> {
            return MathHandoutSlideshowVO.builder()
                    .slideshowId(slideshow.getId())
                    .handoutId(slideshow.getHandoutId())
                    .slideshowPages(listPptHtmlsBySlideshowId(slideshow.getId()))
                    .build();
        }).toList();
    }
}




