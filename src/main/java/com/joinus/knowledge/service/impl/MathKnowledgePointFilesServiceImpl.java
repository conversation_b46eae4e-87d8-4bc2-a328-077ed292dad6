package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.enums.KnowledgePointFileCategory;
import com.joinus.knowledge.model.entity.File;
import com.joinus.knowledge.model.entity.MathKnowledgePointFiles;
import com.joinus.knowledge.model.param.UploadFileParam;
import com.joinus.knowledge.service.FilesService;
import com.joinus.knowledge.service.MathKnowledgePointFilesService;
import com.joinus.knowledge.mapper.MathKnowledgePointFilesMapper;
import jakarta.annotation.Resource;
import org.springframework.http.MediaTypeFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_knowledge_point_files(知识点文件表)】的数据库操作Service实现
* @createDate 2025-08-05 14:13:40
*/
@Service
public class MathKnowledgePointFilesServiceImpl extends ServiceImpl<MathKnowledgePointFilesMapper, MathKnowledgePointFiles>
    implements MathKnowledgePointFilesService{

    @Resource
    private FilesService filesService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveKnowledgePointFileAndRelation(UUID id, List<UploadFileParam> uploadFiles) {
        if (CollUtil.isEmpty(uploadFiles)) {
            return;
        }

        List<MathKnowledgePointFiles> list = new ArrayList<>();
        uploadFiles.forEach(uploadFile -> {
            String objectName = uploadFile.getOssKey();
            String originalType = null;
            String originalMimeType = null;
            KnowledgePointFileCategory category = null;
            if (objectName.endsWith(".jpg") || objectName.endsWith(".png")) {
                originalType = objectName.endsWith(".jpg") ? "jpg" : "png";
                category = KnowledgePointFileCategory.IMAGE;
            } else {
                throw new IllegalArgumentException("不支持的格式");
            }
            originalMimeType = MediaTypeFactory.getMediaType(objectName).orElseThrow().toString();

            File file = filesService.save(objectName.substring(objectName.lastIndexOf("/") + 1), originalType, originalMimeType, uploadFile.getOssKey(), uploadFile.getOssEnum());
            MathKnowledgePointFiles knowledgePointFile = MathKnowledgePointFiles.builder()
                    .knowledgePointId(id)
                    .fileId(file.getId())
                    .category(category)
                    .sortNo(uploadFile.getSortNo())
                    .build();
            list.add(knowledgePointFile);
        });
        remove(lambdaQuery()
                .eq(MathKnowledgePointFiles::getKnowledgePointId, id)
                .eq(MathKnowledgePointFiles::getCategory, list.getFirst().getCategory()).getWrapper());
        saveBatch(list);
    }
}




