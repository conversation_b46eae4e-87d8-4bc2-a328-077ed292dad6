package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.config.base.BusinessException;
import com.joinus.knowledge.mapper.MathKnowledgePointHandoutMapper;
import com.joinus.knowledge.model.entity.MathKnowledgePoint;
import com.joinus.knowledge.model.entity.MathKnowledgePointHandout;
import com.joinus.knowledge.model.param.AddMathHandoutParam;
import com.joinus.knowledge.model.param.MathHandoutSlideParam;
import com.joinus.knowledge.model.vo.MathHandoutSlideshowPageVO;
import com.joinus.knowledge.model.vo.MathHandoutSlideshowVO;
import com.joinus.knowledge.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.function.Consumer;

/**
* <AUTHOR>
* @description 针对表【math_knowledge_point_handouts(讲义表)】的数据库操作Service实现
* @createDate 2025-08-19 13:49:20
*/
@Service
public class MathKnowledgePointHandoutServiceImpl extends ServiceImpl<MathKnowledgePointHandoutMapper, MathKnowledgePointHandout>
    implements MathKnowledgePointHandoutService{

    @Autowired
    private MathHandoutSlideshowPresentationService slideshowPresentationService;
    @Autowired
    private MathHandoutSlideshowPresentationFileService slideshowPresentationFileService;
    @Lazy
    @Autowired
    private MathQuestionsService mathQuestionsService;
    @Autowired
    @Lazy
    private MathKnowledgePointsService mathKnowledgePointsService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<AddMathHandoutParam> params) {
        Consumer<AddMathHandoutParam> addMathHandoutParamConsumer = handoutParam -> {
            MathKnowledgePointHandout handout = lambdaQuery().eq(MathKnowledgePointHandout::getKnowledgePointId, handoutParam.getKnowledgePointId())
                    .one();
            if (null == handout) {
                handout = MathKnowledgePointHandout.builder()
                        .knowledgePointId(handoutParam.getKnowledgePointId())
                        .contentMarkdown("")
                        .build();
                save(handout);
            }

            UUID slideshowId = slideshowPresentationService.saveOrUpdate(handout.getId(), handoutParam.getSlideshowPages());
            slideshowPresentationFileService.saveOrUpdateAudioFiles(slideshowId, handoutParam.getSlideshowPages());
        };
        params.forEach(addMathHandoutParamConsumer);
    }

    @Override
    public List<MathKnowledgePointHandout> queryReviewedWithoutHtmlHandouts(int limit) {
        return baseMapper.queryReviewedWithoutHtmlHandouts(limit)
                .stream()
                .map(handout -> {
                    handout.setContentMarkdown(mathQuestionsService.decodeContentV2(handout.getContentMarkdown()));
                    return handout;
                }).toList();
    }

    @Override
    public List<MathHandoutSlideshowVO> listSlideshows(UUID handoutId) {
        return slideshowPresentationService.listSlideshowsByHandoutId(handoutId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MathHandoutSlideshowVO addSlideshows(UUID handoutId, List<MathHandoutSlideParam> slideshowPages) {
        UUID slideshowId = slideshowPresentationService.saveOrUpdate(handoutId, slideshowPages);
        slideshowPresentationFileService.saveOrUpdateAudioFiles(slideshowId, slideshowPages);
        return slideshowPresentationService.listSlideshowsByHandoutId(handoutId).get(0);
    }

    @Override
    public List<MathHandoutSlideshowPageVO> listPptHtmlsByKnowledgePointId(UUID knowledgePointId) {
        List<MathKnowledgePointHandout> handouts = lambdaQuery().eq(MathKnowledgePointHandout::getKnowledgePointId, knowledgePointId)
                .list();
        if (CollUtil.isEmpty(handouts)) {
            throw new BusinessException("该知识点没有讲义");
        }
        MathKnowledgePointHandout handout = handouts.get(0);
        List<MathHandoutSlideshowVO> mathHandoutSlideshowVOS = slideshowPresentationService.listSlideshowsByHandoutId(handout.getId());
        if (CollUtil.isNotEmpty(mathHandoutSlideshowVOS)) {
            return mathHandoutSlideshowVOS.get(0).getSlideshowPages();
        }
        throw new BusinessException("该知识点没有讲义htmls");
    }

    @Override
    public MathHandoutSlideshowVO getSlideShow(UUID knowledgePointId) {
        List<MathKnowledgePointHandout> handouts = lambdaQuery().eq(MathKnowledgePointHandout::getKnowledgePointId, knowledgePointId)
                .list();
        if (CollUtil.isEmpty(handouts)) {
            throw new BusinessException("该知识点没有讲义");
        }
        MathKnowledgePointHandout handout = handouts.get(0);
        List<MathHandoutSlideshowVO> mathHandoutSlideshowVOS = listSlideshows(handout.getId());
        if (CollUtil.isEmpty(mathHandoutSlideshowVOS)) {
            throw new BusinessException("该知识点没有讲义ppt");
        }

        MathHandoutSlideshowVO mathHandoutSlideshowVO = mathHandoutSlideshowVOS.get(0);
        MathKnowledgePoint knowledgePoint = mathKnowledgePointsService.getById(knowledgePointId);
        mathHandoutSlideshowVO.setKnowledgePointId(knowledgePointId);
        mathHandoutSlideshowVO.setKnowledgePointName(knowledgePoint.getName());
        return mathHandoutSlideshowVO;
    }

}




