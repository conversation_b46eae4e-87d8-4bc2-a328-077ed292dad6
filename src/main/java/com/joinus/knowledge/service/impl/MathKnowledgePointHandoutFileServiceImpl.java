package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.enums.GradeType;
import com.joinus.knowledge.enums.KnowledgePointHandoutFileType;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.enums.SemesterType;
import com.joinus.knowledge.mapper.MathKnowledgePointHandoutFileMapper;
import com.joinus.knowledge.model.entity.File;
import com.joinus.knowledge.model.entity.MathKnowledgePointHandout;
import com.joinus.knowledge.model.entity.MathKnowledgePointHandoutFile;
import com.joinus.knowledge.model.param.UploadFileParam;
import com.joinus.knowledge.model.vo.MathKnowledgePointVO;
import com.joinus.knowledge.service.FilesService;
import com.joinus.knowledge.service.MathHandoutSlideshowPresentationService;
import com.joinus.knowledge.service.MathKnowledgePointHandoutFileService;
import com.joinus.knowledge.service.MathKnowledgePointsService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaTypeFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_knowledge_point_handout_files(知识点讲义文件表)】的数据库操作Service实现
* @createDate 2025-08-19 13:49:20
*/
@Service
public class MathKnowledgePointHandoutFileServiceImpl extends ServiceImpl<MathKnowledgePointHandoutFileMapper, MathKnowledgePointHandoutFile>
    implements MathKnowledgePointHandoutFileService{

    @Resource
    private FilesService filesService;
    @Resource
    private MathHandoutSlideshowPresentationService slideshowPresentationService;
    @Lazy
    @Resource
    private MathKnowledgePointsService mathKnowledgePointsService;
    @Lazy
    @Resource
    private PdfGenerator pdfGenerator;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveKnowledgePointHandoutFileAndRelation(UUID id, List<UploadFileParam> uploadFiles) {
        if (CollUtil.isEmpty(uploadFiles)) {
            return;
        }

        List<MathKnowledgePointHandoutFile> list = new ArrayList<>();
        uploadFiles.forEach(uploadFile -> {
            String objectName = uploadFile.getOssKey();
            String originalType = null;
            String originalMimeType = null;
            KnowledgePointHandoutFileType type = null;
            if (objectName.endsWith(".ppt") || objectName.endsWith(".pptx")) {
                originalType = objectName.endsWith(".ppt") ? "ppt" : "pptx";
                type = KnowledgePointHandoutFileType.PPT;
            } else if (objectName.endsWith(".mp4")) {
                originalType = "mp4";
                type = KnowledgePointHandoutFileType.MP4;
            } else if (objectName.endsWith(".pdf")) {
                originalType = "pdf";
                type = KnowledgePointHandoutFileType.PDF;
            } else if (objectName.endsWith(".html")) {
                originalType = "html";
                type = KnowledgePointHandoutFileType.HTML;
            } else {
                throw new IllegalArgumentException("不支持的格式");
            }
            originalMimeType = MediaTypeFactory.getMediaType(objectName).orElseThrow().toString();

            File file = filesService.save(objectName.substring(objectName.lastIndexOf("/") + 1), originalType, originalMimeType, uploadFile.getOssKey(), uploadFile.getOssEnum());
            MathKnowledgePointHandoutFile mathKnowledgePointHandoutFile = MathKnowledgePointHandoutFile.builder()
                    .id(UUID.randomUUID())
                    .handoutId(id)
                    .fileId(file.getId())
                    .type(type)
                    .build();
            list.add(mathKnowledgePointHandoutFile);
        });
        List<KnowledgePointHandoutFileType> types = list.stream().map(MathKnowledgePointHandoutFile::getType).distinct().toList();
        types.forEach(type -> remove(lambdaQuery()
                .eq(MathKnowledgePointHandoutFile::getHandoutId, id)
                .eq(MathKnowledgePointHandoutFile::getType, type).getWrapper()));
        saveBatch(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateHandoutHtmlAndPdf(MathKnowledgePointHandout handout) {
        String fileName = generateHandoutFileName(handout.getKnowledgePointId());
        String pdfKey = pdfGenerator.generateHandoutPdf(handout.getContentMarkdown(), fileName);
        String htmlKey = pdfGenerator.generateHandoutHtml(handout.getContentMarkdown(), fileName);
        UploadFileParam pdfFileParam = new UploadFileParam();
        pdfFileParam.setOssEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB);
        pdfFileParam.setOssKey(pdfKey);
        UploadFileParam htmlFileParam = new UploadFileParam();
        htmlFileParam.setOssEnum(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB);
        htmlFileParam.setOssKey(htmlKey);
        saveKnowledgePointHandoutFileAndRelation(handout.getId(), List.of(pdfFileParam, htmlFileParam));
    }

    @Override
    public String generateHandoutFileName(UUID knowledgePointId) {
        MathKnowledgePointVO knowledgePointVO = mathKnowledgePointsService.getDetailById(knowledgePointId);
        return StrUtil.format("{}-{}-{}-{}", knowledgePointVO.getPublisherDescription(),
                GradeType.of(knowledgePointVO.getGrade()).getDescription(),
                knowledgePointVO.getGrade() > 9 ? SemesterType.of(knowledgePointVO.getSemester() + 10).getDescription() : SemesterType.of(knowledgePointVO.getSemester()).getDescription(),
                knowledgePointVO.getName());
    }
}




