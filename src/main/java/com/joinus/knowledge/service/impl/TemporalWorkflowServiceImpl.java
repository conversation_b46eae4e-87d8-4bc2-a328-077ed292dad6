package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.joinus.knowledge.common.GlobalConstants;
import com.joinus.knowledge.enums.*;
import com.joinus.knowledge.model.entity.*;
import com.joinus.knowledge.model.param.AnalyzeExamParam;
import com.joinus.knowledge.model.po.ContentPart;
import com.joinus.knowledge.model.response.StreamResponse;
import com.joinus.knowledge.model.vo.QuestionWithLatestAnswerVO;
import com.joinus.knowledge.service.*;
import com.joinus.knowledge.temporal.workflow.*;
import com.joinus.knowledge.utils.ConverterUtils;
import com.joinus.knowledge.utils.MinioUtils;
import com.joinus.knowledge.utils.PromptUtils;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowOptions;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TemporalWorkflowServiceImpl implements TemporalWorkflowService {

    @Resource
    WorkflowClient workflowClient;

    private final int taskTimeOutHours = 2;

    @Resource
    AIChatService aiChatService;

    @Resource
    FilesService filesService;

    @Resource
    MinioUtils minioUtils;

    @Resource
    MathQuestionsService mathQuestionsService;

    @Resource
    MathLabelService mathLabelService;

    @Resource
    PromptUtils promptUtils;

    @Resource
    QuestionLabelService questionLabelService;

    @Resource
    MathAnswersService mathAnswersService;

    @Resource
    QuestionAnswerRelationsService questionAnswerRelationsService;

    @Resource
    MathQuestionRelationshipsService mathQuestionRelationshipsService;

    int ocrCorePoolSize = 10;
    int ocrMaximumPoolSize = 10;
    long keepAliveTime = 0L;
    int ocrQueueCapacity = 300;

    int labelCorePoolSize = 100;
    int labelMaximumPoolSize = 100;
    int labelQueueCapacity = 1000;


    BlockingQueue<Runnable> workQueue = new ArrayBlockingQueue<>(ocrQueueCapacity);

    // 使用AbortPolicy，当任务超过300个等待任务时抛出异常
    ThreadPoolExecutor executor = new ThreadPoolExecutor(
            ocrCorePoolSize,
            ocrMaximumPoolSize,
            keepAliveTime,
            TimeUnit.MILLISECONDS,
            workQueue,
            new ThreadPoolExecutor.AbortPolicy()
    );
    BlockingQueue<Runnable> labelWorkQueue = new ArrayBlockingQueue<>(labelQueueCapacity);

    // 使用AbortPolicy，当任务超过300个等待任务时抛出异常
    ThreadPoolExecutor labelExecutor = new ThreadPoolExecutor(
            labelCorePoolSize,
            labelMaximumPoolSize,
            keepAliveTime,
            TimeUnit.MILLISECONDS,
            labelWorkQueue,
            new ThreadPoolExecutor.AbortPolicy()
    );

    @Autowired
    private MathKnowledgePointsService mathKnowledgePointsService;
    @Autowired
    private QuestionKnowledgePointsService questionKnowledgePointsService;
    @Autowired
    private QuestionFileService questionFileService;
    @Autowired
    private QuestionTypesMappingService questionTypesMappingService;

    @Override
    public void extractInfoFromImageByLLM(String bookId) {
        WorkflowOptions options = WorkflowOptions.newBuilder()
                .setTaskQueue(GlobalConstants.EXTRACT_INFO_FROM_IMAGE_BY_LLM_TASK_QUEUE)
                .setWorkflowId(String.valueOf(System.currentTimeMillis()))
                .setWorkflowTaskTimeout(Duration.ofMinutes(1))
                .setWorkflowExecutionTimeout(Duration.ofHours(taskTimeOutHours))
                .setWorkflowRunTimeout(Duration.ofHours(taskTimeOutHours))
                .build();
        ExtractInfoFromImageByLLMWorkflow workflow = workflowClient.newWorkflowStub(ExtractInfoFromImageByLLMWorkflow.class, options);
        workflow.submit(bookId);
    }


    @Override
    public void analyzeKnowledgePointsForExam(AnalyzeExamParam examParam) {
        WorkflowOptions options = WorkflowOptions.newBuilder()
                .setTaskQueue(GlobalConstants.ANALYZE_EXAM_KNOWLEDGE_POINTS_TASK_QUEUE)
                .setWorkflowId(examParam.getExamId().toString())
                .setWorkflowTaskTimeout(Duration.ofMinutes(1))
                .setWorkflowExecutionTimeout(Duration.ofHours(taskTimeOutHours))
                .setWorkflowRunTimeout(Duration.ofHours(taskTimeOutHours))
                .build();
        AnalyzeExamKnowledgePointsWorkflow workflow = workflowClient.newWorkflowStub(AnalyzeExamKnowledgePointsWorkflow.class, options);
        workflow.submit(examParam);
    }

    @Override
    public void knowledgeDomainLabel(Integer count) {
        // 查询没有打过知识领域标签的数学题，每次查询count条，按照创建时间倒序
        ChatOptions chatOptions = ChatOptions.builder()
                .temperature(0.1)
                .build();
        List<QuestionWithLatestAnswerVO> questionList = mathQuestionsService.listNoKnowledgeDomainLabelQuestions(count);

        // 查询知识领域的标签
        List<MathLabel> labelList = mathLabelService.lambdaQuery()
                .eq(MathLabel::getType, MathLabelType.KNOWLEDGE_DOMAIN.toString())
                .list();
        List<String> labels = labelList.stream().map(MathLabel::getName).toList();

        Map<String, UUID> labelMap = labelList.stream().collect(Collectors.toMap(MathLabel::getName, MathLabel::getId));
        // 给这些题打知识领域的标签
        for (QuestionWithLatestAnswerVO question : questionList) {
            labelExecutor.execute(() -> judgeKnowledgeDomainLabel(question, labels, labelMap, chatOptions));
        }
    }

    public void judgeKnowledgeDomainLabel(QuestionWithLatestAnswerVO question, List<String> labels, Map<String, UUID> labelMap, ChatOptions chatOptions) {
        // 通过题目内容，调用AI接口，获取知识领域标签
        String questionContent = question.getQuestionContent();
        String promptTemplate = promptUtils.getPromptTemplate(PromptEnum.QUESTION_KNOWLEDGE_DOMAIN_LABEL);
        String promptText = StrUtil.format(promptTemplate, questionContent, question.getAnswer(), question.getAnswerContent());
        String chatResult = aiChatService.chat(promptText, AIModelType.JYSD_DEEPSEEK_R1, chatOptions);
        if (StrUtil.isBlank(chatResult)) {
            log.error("模型输出为空");
            return;
        }
        String labelName = ConverterUtils.convertKnowledgeDomain(chatResult);
        UUID labelId = labelMap.get(labelName);
        if (labelId == null) {
            log.error("知识领域标签不存在，labelName:{}", labelName);
            return;
        }
        // 保存知识领域标签
        QuestionLabel questionLabel = QuestionLabel.builder()
                .questionId(question.getQuestionId())
                .labelId(labelId)
                .labelType(MathLabelType.KNOWLEDGE_DOMAIN.toString())
                .build();
        // 判断同类型标签不存在
        List<QuestionLabel> questionLabels = questionLabelService.lambdaQuery()
                .eq(QuestionLabel::getQuestionId, question.getQuestionId())
                .eq(QuestionLabel::getLabelType, MathLabelType.KNOWLEDGE_DOMAIN.toString())
                .list();
        if (CollUtil.isEmpty(questionLabels)) {
            questionLabelService.save(questionLabel);
        }
    }

    @Override
    public void generateQuestionBatch(String labelName, Integer count, String publisher) {
        // 查询指定知识领域标签的数学题，每次查询count条，按照创建时间倒序
        List<QuestionWithLatestAnswerVO> questionList = mathQuestionsService.listMathQuestionByKnowledgeDomain(labelName, count);

        Map<UUID, List<QuestionKnowledgePoint>> questionKnowledgeMap = questionKnowledgePointsService.lambdaQuery()
                .in(QuestionKnowledgePoint::getQuestionId, questionList.stream().map(QuestionWithLatestAnswerVO::getQuestionId).toList())
                .list().stream().collect(Collectors.groupingBy(QuestionKnowledgePoint::getQuestionId));

        List<QuestionWithLatestAnswerVO> originQuestionList = new ArrayList<>();
        //筛选指定版本的知识点
        if (StrUtil.isNotBlank(publisher)) {
            List<MathKnowledgePoint> publisherKnowledgePoints = mathKnowledgePointsService.listByPublisher(publisher);
            for (QuestionWithLatestAnswerVO questionVO :questionList) {
                List<QuestionKnowledgePoint> questionKnowledgePoints = questionKnowledgeMap.get(questionVO.getQuestionId());
                if (CollUtil.isNotEmpty(questionKnowledgePoints)
                        && publisherKnowledgePoints.stream().map(MathKnowledgePoint::getId).toList().contains(questionKnowledgePoints.getFirst().getKnowledgePointId())) {
                    originQuestionList.add(questionVO);
                }
            }
        } else {
            originQuestionList = questionList;
        }

        log.info("originQuestionList size:{}", originQuestionList.size());

        Map<UUID, String> knowledgePointsMap = mathKnowledgePointsService.list().stream().collect(Collectors.toMap(MathKnowledgePoint::getId, MathKnowledgePoint::getName));
        KnowledgeDomainLabel knowledgeDomainLabel = KnowledgeDomainLabel.fromLabel(labelName);

        BlockingQueue<Runnable> queue = new ArrayBlockingQueue<>(2000);

        ThreadPoolExecutor generateExecutor = new ThreadPoolExecutor(
                knowledgeDomainLabel.getPoolSize(),
                knowledgeDomainLabel.getPoolSize(),
                keepAliveTime,
                TimeUnit.MILLISECONDS,
                queue,
                new ThreadPoolExecutor.AbortPolicy()
        );

        originQuestionList.forEach(question -> generateExecutor.execute(() -> generateQuestion(question, questionKnowledgeMap, knowledgePointsMap, knowledgeDomainLabel)));
    }

    public void generateQuestion(QuestionWithLatestAnswerVO question, Map<UUID, List<QuestionKnowledgePoint>> questionKnowledgeMap, Map<UUID, String> knowledgePointsMap, KnowledgeDomainLabel label) {
        String promptTextTemplate = promptUtils.getPromptTemplate(label.getPromptEnum());
        String originQuestion = question.getQuestionContent();
        Set<String> knowledgePoints = new HashSet<>();
        Set<UUID> knowledgePointIds = new HashSet<>();

        List<QuestionKnowledgePoint> questionKnowledgePointList = questionKnowledgeMap.get(question.getQuestionId());

        if (questionKnowledgePointList != null) {
            knowledgePoints = questionKnowledgePointList.stream()
                    .map(questionKnowledgePoint -> {
                        String knowledgePointName = knowledgePointsMap.get(questionKnowledgePoint.getKnowledgePointId());
                        if (knowledgePointName == null) {
                            log.error("知识点不存在，id:{}", questionKnowledgePoint.getKnowledgePointId());
                            return null;
                        }
                        return knowledgePointName;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            knowledgePointIds = questionKnowledgePointList.stream().map(QuestionKnowledgePoint::getKnowledgePointId).collect(Collectors.toSet());
        }
        String promptText = StrUtil.format(promptTextTemplate, originQuestion, knowledgePoints, question.getAnswer(), question.getAnswerContent(), Math.min(10 - question.getDerivedCount(), 5));
        ChatOptions generateChatOption = ChatOptions.builder().temperature(0.8).build();
        AIModelType aiModelType = label.getAiModelType();
        String assistantMessage;
        if (aiModelType == AIModelType.GEMINI_2_5_PRO) {
            /*List<String> imageUrls = new ArrayList<>();
            List<QuestionFile> questionFiles = questionFileService.lambdaQuery()
                    .eq(QuestionFile::getQuestionId, question.getQuestionId())
                    .list();
            if (CollUtil.isNotEmpty(questionFiles)) {
                List<UUID> fileIds = questionFiles.stream().map(QuestionFile::getFileId).toList();
                imageUrls = filesService.lambdaQuery()
                        .in(BaseEntity::getId, fileIds)
                        .list()
                        .stream()
                        .map(file -> minioUtils.getPresignedObjectUrl(GlobalConstants.MINIO_BUCKET_NAME, file.getOssUrl()))
                        .toList();
            }
            assistantMessage = aiChatService.chat(promptText, imageUrls, aiModelType, generateChatOption);*/
            promptText = mathQuestionsService.decodeContentV2(promptText);
            List<ContentPart> contentParts = ConverterUtils.parseContent(promptText);
            assistantMessage = fluxToString(aiChatService.chatStreamWithContentPart(contentParts, aiModelType, generateChatOption, null, null));
        } else {
            assistantMessage = aiChatService.chat(promptText, aiModelType, generateChatOption);
        }

        if (StrUtil.isBlank(assistantMessage)) {
            return;
        }
        String generatedQuestions = genenratedQuestionJsonFormat(assistantMessage);
        //generatedQuestions = ConverterUtils.fixInvalidBackslashesInJson(generatedQuestions);
        JSONArray jsonArray;
        try {
            jsonArray = JSONUtil.parseArray(generatedQuestions);
        } catch (Exception e) {
            log.error("json格式解析错误:{}", generatedQuestions);
            return;
        }

        if (!jsonArray.isEmpty()) {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                this.saveNewQuestion(question, jsonObject, knowledgePointIds, aiModelType, generateChatOption);
            }
        }
    }

    private String fluxToString(Flux<String> flux) {
        final StringBuilder fullContent = new StringBuilder();
        flux.doOnNext(chunk -> {
                    StreamResponse response = JSONUtil.toBean(chunk, StreamResponse.class);
                    if (StrUtil.isNotEmpty(response.getContent())) {
                        fullContent.append(response.getContent());
                    }
                })
                .blockLast(Duration.ofMinutes(10));
        return fullContent.toString();
    }

    private String genenratedQuestionJsonFormat(String input) {
        String promptTextTemplate = promptUtils.getPromptTemplate(PromptEnum.GENERATED_QUESTION_JSON_FORMAT);
        String promptText = StrUtil.format(promptTextTemplate, input);
        String extraBody = """
            {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "questionContent": {"type": "string"},
                        "answer": {"type": "string"},
                        "chainOfThought": {"type": "string"},
                        "difficulty": {"type": "integer"}
                    },
                    "required": ["questionContent", "answer", "chainOfThought", "difficulty"]
                }
            }
            """;
        return aiChatService.chat(promptText, AIModelType.JYSD_QWEN_VL, ChatOptions.builder().temperature(0.1).build(), extraBody);
    }

    @Transactional(rollbackFor = Exception.class)
    protected UUID saveNewQuestion(QuestionWithLatestAnswerVO mathQuestion, JSONObject jsonObject, Set<UUID> knowledgePointIds,
                                   AIModelType aiModelType, ChatOptions chatOptions) {
        // 问题入库
        UUID newQuestionId = UUID.randomUUID();
        MathQuestion newQuestion = MathQuestion.builder()
                .id(newQuestionId)
                .questionType(mathQuestion.getQuestionType() == null? null : QuestionType.getByType(mathQuestion.getQuestionType()))
                .content(jsonObject.getStr("questionContent"))
                .difficulty(jsonObject.getInt("difficulty"))
                .source(QuestionSourceType.AI)
                .existGraphics(false)
                .build();
        mathQuestionsService.save(newQuestion);
        // 答案入库
        UUID questionAnswerId = UUID.randomUUID();
        MathAnswer questionAnswer = new MathAnswer();
        questionAnswer.setId(questionAnswerId);
        questionAnswer.setAnswer(jsonObject.getStr("answer"));
        questionAnswer.setContent(jsonObject.getStr("chainOfThought"));
        mathAnswersService.save(questionAnswer);
        // 问题答案关系入库
        questionAnswerRelationsService.createAssociation(newQuestionId, questionAnswerId);
        // 问题知识点关系入库
        List<QuestionKnowledgePoint> questionKnowledgePoints = knowledgePointIds.stream().map(knowledgePointId -> new QuestionKnowledgePoint(newQuestionId, knowledgePointId)).toList();
        if (CollUtil.isNotEmpty(questionKnowledgePoints)) {
            questionKnowledgePointsService.saveBatch(questionKnowledgePoints);
        }
        // 问题题型关系入库
        List<QuestionTypesMapping> questionTypesMappings = questionTypesMappingService.lambdaQuery()
                .eq(QuestionTypesMapping::getQuestionId, mathQuestion.getQuestionId())
                .list()
                .stream()
                .map(questionTypesMapping ->
                        new QuestionTypesMapping(newQuestionId, questionTypesMapping.getQuestionTypeId())
                )
                .toList();
        if (CollUtil.isNotEmpty(questionTypesMappings)) {
            questionTypesMappingService.saveBatch(questionTypesMappings);
        }
        MathQuestionRelationships mathQuestionRelationships = MathQuestionRelationships.builder()
                .baseQuestionId(mathQuestion.getQuestionId())
                .derivedQuestionId(newQuestion.getId())
                .aiModel(aiModelType.getPgAiModelType())
                .generationParameters(JSONUtil.toJsonStr(chatOptions))
                .build();
        mathQuestionRelationshipsService.save(mathQuestionRelationships);
        log.info("======= {}", newQuestionId);
        return newQuestionId;
    }

    @Override
    public void imageOcr(String bookId) {
        /*WorkflowOptions options = WorkflowOptions.newBuilder()
                .setTaskQueue(GlobalConstants.IMAGE_OCR_TASK_QUEUE)
                .setWorkflowId(String.valueOf(System.currentTimeMillis()))
                .setWorkflowTaskTimeout(Duration.ofMinutes(1))
                .setWorkflowExecutionTimeout(Duration.ofHours(taskTimeOutHours))
                .setWorkflowRunTimeout(Duration.ofHours(taskTimeOutHours))
                .build();
        ImageOcrWorkflow workflow = workflowClient.newWorkflowStub(ImageOcrWorkflow.class, options);
        workflow.submit(bookId);*/
        executeOcr(bookId);
    }

    @Override
    public void reanalyzeExamKnowledgePoint(AnalyzeExamParam examParam) {
        WorkflowOptions options = WorkflowOptions.newBuilder()
                .setTaskQueue(GlobalConstants.MATH_EXAM_REANALYZE_KNOWLWDGE_POINT_TASK_QUEUE)
                .setWorkflowId(examParam.getExamId().toString())
                .setWorkflowTaskTimeout(Duration.ofMinutes(1))
                .setWorkflowExecutionTimeout(Duration.ofHours(taskTimeOutHours))
                .setWorkflowRunTimeout(Duration.ofHours(taskTimeOutHours))
                .build();
        MathExamReanalyzeKnowledgePointWorkflow workflow = workflowClient.newWorkflowStub(MathExamReanalyzeKnowledgePointWorkflow.class, options);
        workflow.submit(examParam);
    }

    private void executeOcr(String bookId) {
        List<Map<String, Object>> files = filesService.getFilesByBookId(bookId);
        files = files.stream().filter(map -> map.get("ocr_html") == null).toList();

        for (Map<String, Object> map : files) {
            executor.execute(() -> ocrHtml(map));
        }
        log.info("ocr end {}", bookId);

    }

    private void ocrHtml(Map<String, Object> map) {
        String promptText = """
            QwenVL HTML
            """;
        String imageUrl = minioUtils.getPresignedObjectUrl(GlobalConstants.MINIO_BUCKET_NAME, map.get("oss_url").toString());
        log.info(imageUrl);
        String assistantText = aiChatService.chat(promptText, imageUrl, AIModelType.JYSD_QWEN_VL);
        Pattern pattern = Pattern.compile("(?s)```(?:\\w+)?\\s*(.*?)\\s*```");
        Matcher matcher = pattern.matcher(assistantText);
        String htmlContent;
        if (matcher.find()) {
            htmlContent = matcher.group(1);
            log.info(htmlContent);
            this.saveOcrHtml(map.get("id").toString(), htmlContent);
        }
    }

    private void saveOcrHtml(String fileId, String html) {
        File file = new File();
        file.setId(UUID.fromString(fileId));
        file.setOcrHtml(html);
        filesService.updateById(file);
    }


    public static void main(String[] args) {
        String input = """
                ```json [ { "difficulty": 3, "questionContent": "已知方程 \\(x^{3a - b} - 4x^{a + 2b} + 6 = 0\\) 是关于 \\(x\\) 的一元二次方程，求 \\(a\\) 和 \\(b\\) 的值。", "answer": "可能的解为：\\n1. \\(\\\\left\\\\{\\\\begin{array}{l}a = \\\\dfrac{5}{7}, \\\\\\\\ b = \\\\dfrac{1}{7}\\\\end{array}\\\\right.\\\\)\\n2. \\(\\\\left\\\\{\\\\begin{array}{l}a = \\\\dfrac{4}{7}, \\\\\\\\ b = -\\\\dfrac{2}{7}\\\\end{array}\\\\right.\\\\)\\n3. \\(\\\\left\\\\{\\\\begin{array}{l}a = \\\\dfrac{6}{7}, \\\\\\\\ b = \\\\dfrac{4}{7}\\\\end{array}\\\\right.\\\\)\\n4. \\(\\\\left\\\\{\\\\begin{array}{l}a = \\\\dfrac{4}{7}, \\\\\\\\ b = \\\\dfrac{5}{7}\\\\end{array}\\\\right.\\\\)\\n5. \\(\\\\left\\\\{\\\\begin{array}{l}a = \\\\dfrac{8}{5}, \\\\\\\\ b = -\\\\dfrac{2}{5}\\\\end{array}\\\\right.\\\\)", "chainOfThought": "1. **分析方程结构**\\n 方程需满足最高次项为 \\(x^2\\)，其他项次数不超过 2。\\n\\n2. **列出指数方程组**\\n - 情况 1：最高次项 \\(3a - b = 2\\)，次高项 \\(a + 2b = 1\\) \\n 解得：\\(\\\\left\\\\{\\\\begin{array}{l}3a - b = 2 \\\\\\\\ a + 2b = 1\\\\end{array}\\\\right.\\\\)，得 \\(a = \\\\dfrac{5}{7}, b = \\\\dfrac{1}{7}\\\\)。\\n - 情况 2：最高次项 \\(3a - b = 2\\)，次高项 \\(a + 2b = 0\\) \\n 解得：\\(\\\\left\\\\{\\\\begin{array}{l}3a - b = 2 \\\\\\\\ a + 2b = 0\\\\end{array}\\\\right.\\\\)，得 \\(a = \\\\dfrac{4}{7}, b = -\\\\dfrac{2}{7}\\\\)。\\n - 情况 3：两项均为 \\(x^2\\)，即 \\(3a - b = 2\\) 且 \\(a + 2b = 2\\) \\n 解得：\\(\\\\left\\\\{\\\\begin{array}{l}3a - b = 2 \\\\\\\\ a + 2b = 2\\\\end{array}\\\\right.\\\\)，得 \\(a = \\\\dfrac{6}{7}, b = \\\\dfrac{4}{7}\\\\)。\\n - 情况 4：最高次项 \\(a + 2b = 2\\)，次高项 \\(3a - b = 1\\) \\n 解得：\\(\\\\left\\\\{\\\\begin{array}{l}a + 2b = 2 \\\\\\\\ 3a - b = 1\\\\end{array}\\\\right.\\\\)，得 \\(a = \\\\dfrac{4}{7}, b = \\\\dfrac{5}{7}\\\\)。\\n - 情况 5：最高次项 \\(a + 2b = 2\\)，次高项 \\(3a - b = 0\\) \\n 解得：\\(\\\\left\\\\{\\\\begin{array}{l}a + 2b = 2 \\\\\\\\ 3a - b = 0\\\\end{array}\\\\right.\\\\)，得 \\(a = \\\\dfrac{8}{5}, b = -\\\\dfrac{2}{5}\\\\)。\\n\\n3. **验证解的合法性**\\n 所有解代入后，指数均为非负整数，方程形式合法。" }, { "difficulty": 3, "questionContent": "已知方程 \\(x^{2m + 3n} - 5x^{m - n} + 2 = 0\\) 是关于 \\(x\\) 的一元二次方程，求 \\(m\\) 和 \\(n\\) 的值。", "answer": "可能的解为：\\n1. \\(\\\\left\\\\{\\\\begin{array}{l}m = 1, \\\\\\\\ n = 0\\\\end{array}\\\\right.\\\\)\\n2. \\(\\\\left\\\\{\\\\begin{array}{l}m = \\\\dfrac{2}{5}, \\\\\\\\ n = \\\\dfrac{2}{5}\\\\end{array}\\\\right.\\\\)\\n3. \\(\\\\left\\\\{\\\\begin{array}{l}m = \\\\dfrac{7}{5}, \\\\\\\\ n = -\\\\dfrac{3}{5}\\\\end{array}\\\\right.\\\\)\\n4. \\(\\\\left\\\\{\\\\begin{array}{l}m = \\\\dfrac{6}{5}, \\\\\\\\ n = -\\\\dfrac{4}{5}\\\\end{array}\\\\right.\\\\)\\n5. \\(\\\\left\\\\{\\\\begin{array}{l}m = \\\\dfrac{8}{5}, \\\\\\\\ n = -\\\\dfrac{2}{5}\\\\end{array}\\\\right.\\\\)", "chainOfThought": "1. **分析方程结构**\\n 方程需满足最高次项为 \\(x^2\\)，其他项次数不超过 2。\\n\\n2. **列出指数方程组**\\n - 情况 1：最高次项 \\(2m + 3n = 2\\)，次高项 \\(m - n = 1\\) \\n 解得：\\(\\\\left\\\\{\\\\begin{array}{l}2m + 3n = 2 \\\\\\\\ m - n = 1\\\\end{array}\\\\right.\\\\)，得 \\(m = 1, n = 0\\\\)。\\n - 情况 2：最高次项 \\(2m + 3n = 2\\)，次高项 \\(m - n = 0\\) \\n 解得：\\(\\\\left\\\\{\\\\begin{array}{l}2m + 3n = 2 \\\\\\\\ m = n\\\\end{array}\\\\right.\\\\)，得 \\(m = n = \\\\dfrac{2}{5}\\\\)。\\n - 情况 3：最高次项 \\(m - n = 2\\)，次高项 \\(2m + 3n = 1\\) \\n 解得：\\(\\\\left\\\\{\\\\begin{array}{l}m - n = 2 \\\\\\\\ 2m + 3n = 1\\\\end{array}\\\\right.\\\\)，得 \\(m = \\\\dfrac{7}{5}, n = -\\\\dfrac{3}{5}\\\\)。\\n - 情况 4：最高次项 \\(m - n = 2\\)，次高项 \\(2m + 3n = 0\\) \\n 解得：\\(\\\\left\\\\{\\\\begin{array}{l}m - n = 2 \\\\\\\\ 2m + 3n = 0\\\\end{array}\\\\right.\\\\)，得 \\(m = \\\\dfrac{6}{5}, n = -\\\\dfrac{4}{5}\\\\)。\\n - 情况 5：两项均为 \\(x^2\\)，即 \\(2m + 3n = 2\\) 且 \\(m - n = 2\\) \\n 解得：\\(\\\\left\\\\{\\\\begin{array}{l}2m + 3n = 2 \\\\\\\\ m - n = 2\\\\end{array}\\\\right.\\\\)，得 \\(m = \\\\dfrac{8}{5}, n = -\\\\dfrac{2}{5}\\\\)。\\n\\n3. **验证解的合法性**\\n 所有解代入后，方程均为有效二次方程。" }, { "difficulty": 3, "questionContent": "已知方程 \\(x^{4a - b} + 2x^{a + b} -3 = 0\\) 是关于 \\(x\\) 的一元二次方程，求 \\(a\\) 和 \\(b\\) 的值。", "answer": "可能的解为：\\n1. \\(\\\\left\\\\{\\\\begin{array}{l}a = \\\\dfrac{4}{5}, \\\\\\\\ b = \\\\dfrac{6}{5}\\\\end{array}\\\\right.\\\\)\\n2. \\(\\\\left\\\\{\\\\begin{array}{l}a = \\\\dfrac{3}{5}, \\\\\\\\ b = \\\\dfrac{2}{5}\\\\end{array}\\\\right.\\\\)\\n3. \\(\\\\left\\\\{\\\\begin{array}{l}a = \\\\dfrac{2}{3}, \\\\\\\\ b = \\\\dfrac{2}{3}\\\\end{array}\\\\right.\\\\)\\n4. \\(\\\\left\\\\{\\\\begin{array}{l}a = 1, \\\\\\\\ b = 2\\\\end{array}\\\\right.\\\\)\\n5. \\(\\\\left\\\\{\\\\begin{array}{l}a = \\\\dfrac{1}{2}, \\\\\\\\ b = 0\\\\end{array}\\\\right.\\\\)", "chainOfThought": "1. **分析方程结构**\\n 方程需满足最高次项为 \\(x^2\\)，其他项次数不超过 2。\\n\\n2. **列出指数方程组**\\n - 情况 1：最高次项 \\(4a - b = 2\\)，次高项 \\(a + b = 1\\) \\n 解得：\\(\\\\left\\\\{\\\\begin{array}{l}4a - b = 2 \\\\\\\\ a + b = 1\\\\end{array}\\\\right.\\\\)，得 \\(a = \\\\dfrac{3}{5}, b = \\\\dfrac{2}{5}\\\\)。\\n - 情况 2：最高次项 \\(4a - b = 2\\)，次高项 \\(a + b = 0\\) \\n 解得：\\(\\\\left\\\\{\\\\begin{array}{l}4a - b = 2 \\\\\\\\ a + b = 0\\\\end{array}\\\\right.\\\\)，得 \\(a = \\\\dfrac{1}{2}, b = 0\\\\)。\\n - 情况 3：最高次项 \\(a + b = 2\\)，次高项 \\(4a - b = 1\\) \\n 解得：\\(\\\\left\\\\{\\\\begin{array}{l}a + b = 2 \\\\\\\\ 4a - b = 1\\\\end{array}\\\\right.\\\\)，得 \\(a = \\\\dfrac{3}{5}, b = \\\\dfrac{7}{5}\\\\)。\\n - 情况 4：最高次项 \\(a + b = 2\\)，次高项 \\(4a - b = 0\\) \\n 解得：\\(\\\\left\\\\{\\\\begin{array}{l}a + b = 2 \\\\\\\\ 4a - b = 0\\\\end{array}\\\\right.\\\\)，得 \\(a = \\\\dfrac{2}{3}, b = \\\\dfrac{2}{3}\\\\)。\\n - 情况 5：两项均为 \\(x^2\\)，即 \\(4a - b = 2\\) 且 \\(a + b = 2\\) \\n 解得：\\(\\\\left\\\\{\\\\begin{array}{l}4a - b = 2 \\\\\\\\ a + b = 2\\\\end{array}\\\\right.\\\\)，得 \\(a = 1, b = 2\\\\)。\\n\\n3. **验证解的合法性**\\n 所有解代入后，方程均为有效二次方程。" } ] ```
                """;
        String generatedQuestions = ConverterUtils.convertToJsonStr(input);
        String escapedJson = generatedQuestions
                .replace("\\", "\\\\")
                .replace("\n", "\\n")
                .replace("\t", "\\t");
        JSONArray jsonArray = null;
        try {
            jsonArray = JSONUtil.parseArray(escapedJson);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(jsonArray);
    }
}
