package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.mapper.SectionKnowledgePointsMapper;
import com.joinus.knowledge.model.entity.SectionKnowledgePoint;
import com.joinus.knowledge.model.po.SectionKnowledgePointPO;
import com.joinus.knowledge.service.SectionKnowledgePointsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【math_section_knowledge_points】的数据库操作Service实现
* @createDate 2025-03-06 16:06:22
*/
@Service
public class SectionKnowledgePointsServiceImpl extends ServiceImpl<SectionKnowledgePointsMapper, SectionKnowledgePoint>
    implements SectionKnowledgePointsService{

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createAssociation(UUID sectionId, UUID knowledgePointId) {
        // 先检查是否已存在
        LambdaQueryWrapper<SectionKnowledgePoint> eq = Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                .eq(SectionKnowledgePoint::getSectionId, sectionId)
                .eq(SectionKnowledgePoint::getKnowledgePointId, knowledgePointId);
        List<SectionKnowledgePoint> sectionKnowledgePoints = baseMapper.selectList(eq);

        if (CollUtil.isNotEmpty(sectionKnowledgePoints)) {
            // 已存在则返回成功
            return true;
        }

        // 创建新实体并保存
        SectionKnowledgePoint newRelation = new SectionKnowledgePoint();
        newRelation.setSectionId(sectionId);
        newRelation.setKnowledgePointId(knowledgePointId);
        return save(newRelation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateAssociationsBySectionId(UUID sectionId, List<UUID> knowledgePointIds) {
        // 删除该小节的所有关联
        remove(lambdaQuery().eq(SectionKnowledgePoint::getSectionId, sectionId));

        // 没有新的关联
        if (knowledgePointIds == null || knowledgePointIds.isEmpty()) {
            return true;
        }

        // 添加新的关联
        List<SectionKnowledgePoint> relations = new ArrayList<>();
        for (UUID KnowledgePointId : knowledgePointIds) {
            SectionKnowledgePoint relation = new SectionKnowledgePoint();
            relation.setSectionId(sectionId);
            relation.setKnowledgePointId(KnowledgePointId);
            relations.add(relation);
        }

        return saveBatch(relations);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateAssociationsByKnowledgePointId(UUID KnowledgePointId, List<UUID> sectionIds) {
        // 删除该考点的所有关联
        remove(lambdaQuery().eq(SectionKnowledgePoint::getKnowledgePointId, KnowledgePointId));

        // 没有新的关联
        if (sectionIds == null || sectionIds.isEmpty()) {
            return true;
        }

        // 添加新的关联
        List<SectionKnowledgePoint> relations = new ArrayList<>();
        for (UUID sectionId : sectionIds) {
            SectionKnowledgePoint relation = new SectionKnowledgePoint();
            relation.setSectionId(sectionId);
            relation.setKnowledgePointId(KnowledgePointId);
            relations.add(relation);
        }

        return saveBatch(relations);
    }

    @Override
    public boolean deleteAssociation(UUID sectionId, UUID knowledgePointId) {
        return remove(lambdaQuery()
                .eq(SectionKnowledgePoint::getSectionId, sectionId)
                .eq(SectionKnowledgePoint::getKnowledgePointId, knowledgePointId));
    }

    @Override
    public boolean deleteAssociationsBySectionId(UUID sectionId) {
        return remove(lambdaQuery().eq(SectionKnowledgePoint::getSectionId, sectionId));
    }

    @Override
    public boolean deleteAssociationsByKnowledgePointId(UUID knowledgePointId) {
        return remove(lambdaQuery().eq(SectionKnowledgePoint::getKnowledgePointId, knowledgePointId).getWrapper());
    }

    @Override
    public List<UUID> getKnowledgePointIdsBySectionId(UUID sectionId) {
        List<SectionKnowledgePoint> relations = lambdaQuery().eq(SectionKnowledgePoint::getSectionId, sectionId).list();
        return relations.stream()
                .map(SectionKnowledgePoint::getKnowledgePointId)
                .collect(Collectors.toList());
    }

    @Override
    public List<UUID> getSectionIdsByKnowledgePointId(UUID knowledgePointId) {
        List<SectionKnowledgePoint> relations = lambdaQuery().eq(SectionKnowledgePoint::getKnowledgePointId, knowledgePointId).list();
        return relations.stream()
                .map(SectionKnowledgePoint::getSectionId)
                .collect(Collectors.toList());
    }

    @Override
    public void createRelation(UUID sectionId, UUID knowledgePointId, Integer pageIndex) {
        LambdaQueryWrapper<SectionKnowledgePoint> wrapper = Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                .eq(SectionKnowledgePoint::getSectionId, sectionId)
                .eq(SectionKnowledgePoint::getKnowledgePointId, knowledgePointId)
                .eq(SectionKnowledgePoint::getPageIndex, pageIndex);
        Long count = baseMapper.selectCount(wrapper);
        if (count == 0) {
            SectionKnowledgePoint sectionKnowledge = SectionKnowledgePoint.builder()
                    .knowledgePointId(knowledgePointId)
                    .sectionId(sectionId)
                    .pageIndex(pageIndex)
                    .build();
            baseMapper.insert(sectionKnowledge);
        }
    }

    @Override
    public void deleteAssociation(UUID sectionId, UUID knowledgePointId, Integer pageIndex) {
        LambdaQueryWrapper<SectionKnowledgePoint> wrapper = Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                .eq(SectionKnowledgePoint::getSectionId, sectionId)
                .eq(SectionKnowledgePoint::getKnowledgePointId, knowledgePointId)
                .eq(SectionKnowledgePoint::getPageIndex, pageIndex);
        Long count = baseMapper.selectCount(wrapper);
        if (count > 0) {
            baseMapper.delete(wrapper);
        }
    }

    @Override
    public void removeEntity(SectionKnowledgePointPO sectionKp) {
        LambdaQueryWrapper<SectionKnowledgePoint> wrapper = Wrappers.lambdaQuery(SectionKnowledgePoint.class)
                .eq(SectionKnowledgePoint::getSectionId, sectionKp.getSectionId())
                .eq(SectionKnowledgePoint::getKnowledgePointId, sectionKp.getId())
                .eq(null != sectionKp.getPageIndex(), SectionKnowledgePoint::getPageIndex, sectionKp.getPageIndex())
                .isNull(null == sectionKp.getPageIndex(), SectionKnowledgePoint::getPageIndex);
        Long count = baseMapper.selectCount(wrapper);
        if (count > 0) {
            lambdaUpdate().set(SectionKnowledgePoint::getRemain, false)
                    .eq(SectionKnowledgePoint::getSectionId, sectionKp.getSectionId())
                    .eq(SectionKnowledgePoint::getKnowledgePointId, sectionKp.getId())
                    .eq(null != sectionKp.getPageIndex(), SectionKnowledgePoint::getPageIndex, sectionKp.getPageIndex())
                    .isNull(null == sectionKp.getPageIndex(), SectionKnowledgePoint::getPageIndex)
                    .update();
        }

    }
}




