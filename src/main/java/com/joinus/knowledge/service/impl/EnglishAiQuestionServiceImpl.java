package com.joinus.knowledge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.mapper.EnglishAiQuestionMapper;
import com.joinus.knowledge.mapper.EnglishKnowledgePointsMapper;
import com.joinus.knowledge.model.entity.EnglishAiQuestion;
import com.joinus.knowledge.model.entity.EnglishKnowledgePoints;
import com.joinus.knowledge.model.param.EnableOrDisableEnglishAiQuestionParam;
import com.joinus.knowledge.model.param.EnglishAiQuestionPageQueryParam;
import com.joinus.knowledge.model.vo.EnglishAiQuestionDetailVO;
import com.joinus.knowledge.model.vo.EnglishAiQuestionVO;
import com.joinus.knowledge.service.EnglishAiQuestionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【english_ai_question(英语ai试题表)】的数据库操作Service实现
 * @createDate 2025-08-01 18:15:37
 */
@Service
public class EnglishAiQuestionServiceImpl extends ServiceImpl<EnglishAiQuestionMapper, EnglishAiQuestion>
        implements EnglishAiQuestionService {

    @Resource
    private EnglishKnowledgePointsMapper englishKnowledgePointsMapper;

    @Override
    public IPage<EnglishAiQuestionVO> pageQuery(EnglishAiQuestionPageQueryParam param) {
        Page<EnglishAiQuestionVO> page = new Page<>(param.getPage(), param.getSize());
        return baseMapper.pageQuery(page, param);
    }

    @Override
    public EnglishAiQuestionDetailVO queryDetailById(UUID id) {
        EnglishAiQuestion entity = baseMapper.selectById(id);
        Assert.notNull(entity, "题目不存在");
        EnglishAiQuestionDetailVO vo = BeanUtil.copyProperties(entity, EnglishAiQuestionDetailVO.class);
        if (StrUtil.isBlank(vo.getTitle())) {
            vo.setTitle(entity.getContent());
        }
        List<EnglishKnowledgePoints> points = englishKnowledgePointsMapper.listByQuestionId(id);
        if (CollectionUtil.isNotEmpty(points)) {
            String pointNames = points.stream().map(EnglishKnowledgePoints::getName).collect(Collectors.joining(" + "));
            vo.setKnowledgePoints(pointNames);
        }
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchEnable(EnableOrDisableEnglishAiQuestionParam param) {
        LambdaUpdateWrapper<EnglishAiQuestion> wrapper = Wrappers.lambdaUpdate(EnglishAiQuestion.class)
                .in(EnglishAiQuestion::getId, param.getUuidList())
                .set(EnglishAiQuestion::getEnabled, 1)
                .set(EnglishAiQuestion::getUpdatedAt, LocalDateTime.now());
        this.baseMapper.update(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchDisable(EnableOrDisableEnglishAiQuestionParam param) {
        LambdaUpdateWrapper<EnglishAiQuestion> wrapper = Wrappers.lambdaUpdate(EnglishAiQuestion.class)
                .in(EnglishAiQuestion::getId, param.getUuidList())
                .set(EnglishAiQuestion::getEnabled, 0)
                .set(EnglishAiQuestion::getUpdatedAt, LocalDateTime.now());
        this.baseMapper.update(wrapper);
    }
}
