package com.joinus.knowledge.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.joinus.knowledge.enums.OssEnum;
import com.joinus.knowledge.mapper.FilesMapper;
import com.joinus.knowledge.mapper.MathHandoutSlideshowPresentationFileMapper;
import com.joinus.knowledge.model.entity.File;
import com.joinus.knowledge.model.entity.MathHandoutSlideshowPresentationFile;
import com.joinus.knowledge.model.param.MathHandoutSlideParam;
import com.joinus.knowledge.model.param.MathHandoutVoiceParam;
import com.joinus.knowledge.service.FilesService;
import com.joinus.knowledge.service.MathHandoutSlideshowPresentationFileService;
import com.joinus.knowledge.utils.FileUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【math_handout_slideshow_presentation_files】的数据库操作Service实现
 * @createDate 2025-08-19 13:49:20
 */
@Service
public class MathHandoutSlideshowPresentationFileServiceImpl extends ServiceImpl<MathHandoutSlideshowPresentationFileMapper, MathHandoutSlideshowPresentationFile>
        implements MathHandoutSlideshowPresentationFileService {

    @Autowired
    private FilesMapper filesMapper;

    @Override
    public void saveOrUpdateAudioFiles(UUID id, List<MathHandoutSlideParam> slides) {
        List<MathHandoutVoiceParam> voices = slides.stream().filter(slide -> CollUtil.isNotEmpty(slide.getVoices()))
                .flatMap(slide -> slide.getVoices().stream())
                .collect(Collectors.toList());
        List<MathHandoutSlideshowPresentationFile> existVoices = lambdaQuery().eq(MathHandoutSlideshowPresentationFile::getMathHandoutSlideshowPresentationId, id)
                .list();
        if (CollUtil.isNotEmpty(existVoices)) {
            //删除已存在的文件关系及文件
            List<UUID> toDeleteFileIds = existVoices.stream().map(MathHandoutSlideshowPresentationFile::getFileId).toList();
            toDeleteFileIds.stream().forEach(fileId -> {
                //删除文件
                filesMapper.deleteById(fileId);
            });
            remove(Wrappers.lambdaQuery(MathHandoutSlideshowPresentationFile.class)
                    .eq(MathHandoutSlideshowPresentationFile::getMathHandoutSlideshowPresentationId, id)
                    .in(MathHandoutSlideshowPresentationFile::getFileId, toDeleteFileIds));
        }

        //添加新文件及关系
        voices.stream().forEach(voiceparam -> {
            File file = File.builder().name(FileUtil.getFileNameFromOssKey(voiceparam.getOssKey()))
                    .type(FileUtil.getFileExtension(voiceparam.getOssKey()))
                    .ossUrl(voiceparam.getOssKey())
                    .mimeType(FileUtil.getMimeTypeByFileName(voiceparam.getOssKey()))
                    .ossBucket(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB.getBucket())
                    .ossType(OssEnum.ALIYUN_EDU_KNOWLEDGE_HUB.getType())
                    .build();
            filesMapper.insert(file);
            baseMapper.insert(MathHandoutSlideshowPresentationFile.builder()
                    .id(UUID.randomUUID())
                    .mathHandoutSlideshowPresentationId(id)
                    .fileId(file.getId())
                    .type("AUDIO")
                    .build());
        });

    }
}




