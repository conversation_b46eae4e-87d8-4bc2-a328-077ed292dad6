package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.entity.MathHandoutSlideshowPresentationFile;
import com.joinus.knowledge.model.param.MathHandoutSlideParam;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_handout_slideshow_presentation_files】的数据库操作Service
* @createDate 2025-08-19 13:49:20
*/
public interface MathHandoutSlideshowPresentationFileService extends IService<MathHandoutSlideshowPresentationFile> {

    void saveOrUpdateAudioFiles(UUID id, List<MathHandoutSlideParam> slides);
}
