package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.entity.MathHandoutSlideshowPresentation;
import com.joinus.knowledge.model.param.MathHandoutSlideParam;
import com.joinus.knowledge.model.vo.MathHandoutSlideshowPageVO;
import com.joinus.knowledge.model.vo.MathHandoutSlideshowVO;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_handout_slideshow_presentations(讲义幻灯片展示表)】的数据库操作Service
* @createDate 2025-08-19 13:49:20
*/
public interface MathHandoutSlideshowPresentationService extends IService<MathHandoutSlideshowPresentation> {

    UUID saveOrUpdate(UUID id, List<MathHandoutSlideParam> slides);

    List<MathHandoutSlideshowPageVO> listPptHtmlsBySlideshowId(UUID slideshowId);

    List<MathHandoutSlideshowPageVO> updatePptHtmls(UUID id, List<MathHandoutSlideParam> slides);

    List<MathHandoutSlideshowVO> listSlideshowsByHandoutId(UUID id);
}
