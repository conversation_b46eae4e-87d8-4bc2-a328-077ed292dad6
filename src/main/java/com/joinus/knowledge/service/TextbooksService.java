package com.joinus.knowledge.service;

import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathCatalogNodes;
import com.joinus.knowledge.model.entity.Textbooks;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.param.QueryTextbookParam;
import com.joinus.knowledge.model.vo.SectionKeypointVO;
import com.joinus.knowledge.model.vo.SectionVO;
import com.joinus.knowledge.model.vo.TextbookPointVO;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_textbooks】的数据库操作Service
* @createDate 2025-03-06 16:06:22
*/
public interface TextbooksService extends IService<Textbooks> {
    /**
     * 根据名称模糊查询教材
     * @param name 教材名称
     * @return 匹配的教材列表
     */
    List<Textbooks> findByNameLike(String name);
    
    /**
     * 根据出版社查询教材
     * @param publisher 出版社名称
     * @return 匹配的教材列表
     */
    List<Textbooks> findByPublisher(List<String> publisher);
    
    /**
     * 根据学科查询教材
     * @param subject 学科名称
     * @return 匹配的教材列表
     */
    List<Textbooks> findBySubject(String subject);
    
    /**
     * 逻辑删除教材
     * @param id 教材ID
     * @return 操作是否成功
     */
    boolean logicalDelete(UUID id);
    
    /**
     * 恢复逻辑删除的教材
     * @param id 教材ID
     * @return 操作是否成功
     */
    boolean restore(UUID id);
    
    /**
     * 根据教材ID和文件夹路径导入图片，并建立图片与教材的关联关系
     * 文件夹结构必须遵循：
     *   -- bookName
     *     -- pageNo
     *       -- origin.png
     * 
     * @param textbookId 教材ID
     * @param folderPath 图片文件夹路径
     * @return 成功导入的页面数量
     * @throws Exception 如果导入过程中出现错误
     */
    int importImagesAndLinkToTextbook(UUID textbookId, String folderPath) throws Exception;
    
    /**
     * 根据教材ID查询所有页面信息，包括页码和图片地址
     * 
     * @param textbookId 教材ID
     * @return 页面信息列表，包含页码和图片URL地址
     */
    List<Map<String, Object>> getTextbookPages(UUID textbookId);


    List<TextbookPointVO> getKeypointsByBookIdAndPageNo(String bookId, Integer pageNo);

    TextbookPointVO getKeypointsByKeypointId(String type, UUID keypointId, UUID textbookId, Integer pageNo);

    boolean checkExistPage(UUID textbookId, int pageNo);

    List<SectionKeypointVO> listAllKnowledgePointsAndQuestionTypes(UUID id);

    List<MathCatalogNodes> listAllChapters(UUID id);

    List<SectionVO> listAllSections(UUID id);

    List<Textbooks> listTextbook(QueryTextbookParam queryTextbookParam);

    Map<String, Object> queryElectronicTextbook(PublisherType publisher, Integer grade, Integer semester);
}
