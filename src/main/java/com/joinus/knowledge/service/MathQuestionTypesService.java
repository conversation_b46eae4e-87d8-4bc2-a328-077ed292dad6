package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.dto.MathQuestionTypesSlimDTO;
import com.joinus.knowledge.model.entity.MathQuestionType;
import com.joinus.knowledge.model.param.MathQuestionTypeParam;
import com.joinus.knowledge.model.param.PageQuestionTypeParam;
import com.joinus.knowledge.model.po.MathQuestionTypePO;
import com.joinus.knowledge.model.vo.MathQuestionTypeVO;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_question_types】的数据库操作Service
* @createDate 2025-02-28 14:12:06
*/
public interface MathQuestionTypesService extends IService<MathQuestionType> {
    
    /**
     * 获取精简的题型列表，只返回id、name和sortNo字段
     * @return 精简题型列表
     */
    List<MathQuestionTypesSlimDTO> listSlim();

    void deleteById(UUID keypointId);

    MathQuestionType save(Integer pageNo, String name, UUID textbookId, Integer sortNo);

    List<MathQuestionType> listQuestionTypesByQuestionId(UUID questionId);

    Map<UUID, List<MathQuestionTypeVO>> listQuestionTypeIds(List<UUID> questionIds);

    List<MathQuestionType> listByQuestionId(UUID id);

    List<MathQuestionType> listByTextbookId(UUID textbookId);

    /**
     * 根据题目ID列表查询题型信息
     *
     * @param ids       题目ID列表
     * @return 题型信息列表
     */
    List<MathQuestionTypePO> listByQuestionIds(List<UUID> ids);

    List<MathQuestionTypePO> listByQuestionIdsAndPublisher(List<UUID> ids, PublisherType publisher);

    List<MathQuestionTypeVO> list(String name, Integer grade, Integer semester, PublisherType publisher, UUID chapterId, String chapterName, UUID sectionId, String sectionName);

    List<MathQuestionTypeVO> listByIds(List<UUID> ids);

    List<MathQuestionTypeVO> listByIdsAndPublisher(List<UUID> ids, PublisherType publisher, Integer grade, Integer semester);

    List<MathQuestionTypeVO> listEnableAiQuestionCountByKnowledgePointIds(List<UUID> knowledgePointIds);

    List<MathQuestionTypeVO> listBySectionIds(List<UUID> list);

    Page<MathQuestionTypeVO> page(Page<MathQuestionTypeVO> pageParam, PageQuestionTypeParam param);

    boolean update(MathQuestionTypeParam questionTypeParam);
}
