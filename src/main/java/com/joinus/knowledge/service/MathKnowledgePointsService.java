package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathKnowledgePoint;
import com.joinus.knowledge.model.param.MathKnowledgePointParam;
import com.joinus.knowledge.model.param.PageKnowledgePointParam;
import com.joinus.knowledge.model.param.SectionKnowledgePointParam;
import com.joinus.knowledge.model.vo.MathKeyPointVO;
import com.joinus.knowledge.model.vo.MathKnowledgePointVO;
import com.joinus.knowledge.model.vo.SectionKnowledgePointVO;
import com.joinus.knowledge.model.po.MathKnowledgePointPO;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_knowledge_points】的数据库操作Service
* @createDate 2025-02-28 14:12:06
*/
public interface MathKnowledgePointsService extends IService<MathKnowledgePoint> {

    MathKnowledgePoint save(Integer pageNo, String name, UUID textbookId, Integer sortNo, boolean isExamPoint);

    List<MathKnowledgePoint> listByGradeAndSemester(Integer grade, Integer semester, PublisherType publisher);

    List<MathKnowledgePoint> listByQuestionId(UUID questionId);

    List<MathKnowledgePoint> listByPublisher(String publisher);

    List<MathKnowledgePointVO> list(String name, Integer grade, Integer semester, PublisherType publisher, UUID chapterId, String chapterName, UUID sectionId, String sectionName);


    List<MathKnowledgePoint> listByTextbookId(UUID textbookId);

    List<MathKnowledgePointPO> listByQuestionIds(List<UUID> ids);

    List<MathKnowledgePointPO> listByQuestionIdsAndPublisher(List<UUID> ids, PublisherType publisher);

    List<MathKnowledgePointVO> listQuestionTypeByKnowledgePointIds(List<UUID> knowledgePoints);

    List<SectionKnowledgePointVO> listSectionKnowledgePointByKnowledgeIds(SectionKnowledgePointParam param);

    Map<String, Object> listQuestionTypeByKnowledgePointIdsV2(List<UUID> knowledgePointIds);

    List<MathKnowledgePointVO> listBySectionIds(List<UUID> sectionIds);

    List<MathKnowledgePointVO> listByIds(List<UUID> ids);

    List<MathKnowledgePointVO> listByIdsAndPublisher(List<UUID> ids, PublisherType publisher, Integer grade, Integer semester);

    List<MathKnowledgePointVO> listKnowledgePointByQuestionId(UUID questionId, Integer grade, Integer semester, PublisherType publisher);

    Page<MathKnowledgePointVO> page(Page<MathKnowledgePointVO> pageParam, PageKnowledgePointParam param);

    boolean update(MathKnowledgePointParam knowledgePointParam);

    MathKnowledgePointVO getDetailById(UUID id);

    List<MathKeyPointVO> getPrimarySchoolBeforeKnowledgePoints(UUID id);

    List<MathKeyPointVO> getPrimarySchoolAfterKnowledgePoints(UUID id);

    List<MathKeyPointVO> getPrimarySchoolBeforeKnowledgePointsByQuestionType(UUID id);

    List<MathKeyPointVO> getPrimarySchoolAfterKnowledgePointsByQuestionType(UUID id);

}
