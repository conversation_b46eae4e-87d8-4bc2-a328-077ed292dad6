package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.enums.PublisherType;
import com.joinus.knowledge.model.entity.MathCatalogNodes;
import com.joinus.knowledge.model.param.*;
import com.joinus.knowledge.model.vo.MathCatalogNodeVO;
import com.joinus.knowledge.model.vo.MathChapterVO;
import com.joinus.knowledge.model.vo.MathSectionVO;
import com.joinus.knowledge.model.vo.SectionKeypointVO;
import com.joinus.knowledge.model.vo.SectionVO;
import jakarta.validation.Valid;
import com.joinus.knowledge.model.vo.SimpleTreeVO;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_catalog_nodes(数学目录节点表)】的数据库操作Service
* @createDate 2025-07-30 17:33:40
*/
public interface MathCatalogNodesService extends IService<MathCatalogNodes> {

    List<MathCatalogNodeVO> list(PublisherType publisher, Integer grade, Integer semester);

    /**
     * 根据教材ID查询所有章节（level=1且textbook_id不为空）
     */
    List<MathCatalogNodes> listAllChaptersByBookId(UUID textbookId);

    /**
     * 查询章节列表（用于替代原MathChapterService.list方法）
     */
    List<MathChapterVO> listChapters(String name, Integer grade, Integer semester, PublisherType publisher);

    /**
     * 根据教材ID查询所有小节（叶节点）
     */
    List<SectionVO> listAllSectionsByBookId(UUID bookId);

    /**
     * 查询小节列表（用于替代原MathSectionService.list方法）
     */
    List<MathSectionVO> listSections(String name, Integer grade, Integer semester, PublisherType publisher, String chapterName, UUID chapterId);

    /**
     * 根据页码查询小节
     */
    List<MathCatalogNodes> getByPageNo(Integer pageNo, UUID textbookId, UUID sectionId);

    /**
     * 根据小节ID查询关键点
     */
    List<SectionKeypointVO> listKeypointsById(UUID sectionId);

    /**
     * 根据出版社查询所有小节
     */
    List<SectionVO> listAllSectionsByPublisher(PublisherType publisher);

    /**
     * 根据条件查询小节
     */
    List<SectionVO> listSectionsByCondition(Integer grade, PublisherType publisher, Integer semester);

    List<MathCatalogNodes> listByTextbookId(UUID textbookId);

    void updateSectionKeypointMapping(UpdateSectionKeypointParam param);

    void deleteSectionKeypointMapping(@Valid DeleteSectionKeypointParam param);

    void switchKeypointType(@Valid SwitchKeypointTypeParam param);

    void updatePageIndex(@Valid UpdateSectionKeypointParam param);

    UUID combineKeypoints(@Valid CombineKeypointParam param);

    void addKeypoints(@Valid AddSectionKeypointParam param);

    List<SimpleTreeVO> tree(PublisherType publisher, Integer grade, Integer semester);

}
