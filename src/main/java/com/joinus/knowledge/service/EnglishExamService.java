package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.entity.EnglishExam;
import com.joinus.knowledge.model.param.EnglishExamPageQueryParam;
import com.joinus.knowledge.model.param.UpdateEnglishExamParam;
import com.joinus.knowledge.model.vo.EnglishExamVO;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description 针对表【en_flow_exam(英语试卷表)】的数据库操作Service
 * @createDate 2025-09-01 18:15:37
 */
public interface EnglishExamService extends IService<EnglishExam> {

    IPage<EnglishExamVO> pageQuery(EnglishExamPageQueryParam param);

    void updateExamDetail(UUID id, UpdateEnglishExamParam param);

    EnglishExamVO queryExamDetailById(UUID id);

    List<String> listSchool(String schoolName);
}
