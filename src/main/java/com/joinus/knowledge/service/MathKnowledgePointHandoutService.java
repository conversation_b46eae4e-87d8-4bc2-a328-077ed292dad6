package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.entity.MathKnowledgePointHandout;
import com.joinus.knowledge.model.param.AddMathHandoutParam;
import com.joinus.knowledge.model.param.MathHandoutSlideParam;
import com.joinus.knowledge.model.vo.MathHandoutSlideshowPageVO;
import com.joinus.knowledge.model.vo.MathHandoutSlideshowVO;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_knowledge_point_handouts(讲义表)】的数据库操作Service
* @createDate 2025-08-19 13:49:20
*/
public interface MathKnowledgePointHandoutService extends IService<MathKnowledgePointHandout> {

    void batchAdd(List<AddMathHandoutParam> params);

    List<MathKnowledgePointHandout> queryReviewedWithoutHtmlHandouts(int limit);

    List<MathHandoutSlideshowVO> listSlideshows(UUID handoutId);

    MathHandoutSlideshowVO addSlideshows(UUID handoutId, List<MathHandoutSlideParam> slideshowPages);

    List<MathHandoutSlideshowPageVO> listPptHtmlsByKnowledgePointId(UUID knowledgePointId);

    MathHandoutSlideshowVO getSlideShow(UUID knowledgePointId);
}
