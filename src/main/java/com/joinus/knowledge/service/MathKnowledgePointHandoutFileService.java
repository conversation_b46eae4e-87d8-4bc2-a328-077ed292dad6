package com.joinus.knowledge.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.joinus.knowledge.model.entity.MathKnowledgePointHandout;
import com.joinus.knowledge.model.entity.MathKnowledgePointHandoutFile;
import com.joinus.knowledge.model.param.UploadFileParam;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【math_knowledge_point_handout_files(知识点讲义文件表)】的数据库操作Service
* @createDate 2025-08-19 13:49:20
*/
public interface MathKnowledgePointHandoutFileService extends IService<MathKnowledgePointHandoutFile> {

    @Transactional(rollbackFor = Exception.class)
    void saveKnowledgePointHandoutFileAndRelation(UUID id, List<UploadFileParam> uploadFiles);

    void generateHandoutHtmlAndPdf(MathKnowledgePointHandout handout);

    String generateHandoutFileName(UUID knowledgePointId);
}
