<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.EnglishAiQuestionMapper">
    <!-- 试卷分页条件查询 -->
    <select id="pageQuery" resultType="com.joinus.knowledge.model.vo.EnglishAiQuestionVO">
        select t.id,
               t.source_type sourceType,
               t.grade,
               t.type,
               t.content,
               t.difficulty,
               t.enabled,
               t.semester,
               t.created_at  createdAt,
               t.status,
               t.explain,
               t.error_type errorType,
               t.severity
        from english_ai_question t
        where t.deleted_at is null
          and t.status = true
        <if test="param.content != null and param.content != ''">
            AND t.content LIKE CONCAT('%', #{param.content}, '%')
        </if>
        <if test="param.sourceType != null">
            AND t.source_type = #{param.sourceType}
        </if>
        <if test="param.grade != null">
            AND t.grade = #{param.grade}
        </if>
        <if test="param.semester != null and param.semester != ''">
            AND t.semester = #{param.semester}
        </if>
        <if test="param.difficulty != null">
            AND t.difficulty = #{param.difficulty}
        </if>
        <if test="param.enabled != null">
            AND t.enabled = #{param.enabled}
        </if>
        <if test="param.type != null and param.type != ''">
            AND t.type = #{param.type}
        </if>
        ORDER BY t.created_at DESC
    </select>
</mapper>
