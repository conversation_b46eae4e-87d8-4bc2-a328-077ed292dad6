<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathHandoutSlideshowPresentationFileMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.MathHandoutSlideshowPresentationFile">
            <result property="id" column="id" />
            <result property="mathHandoutSlideshowPresentationId" column="math_handout_slideshow_presentation_id" />
            <result property="type" column="type" />
            <result property="fileId" column="file_id" />
            <result property="createdAt" column="created_at" />
            <result property="updatedAt" column="updated_at" />
            <result property="deletedAt" column="deleted_at" />
    </resultMap>

    <sql id="Base_Column_List">
        id,math_handout_slideshow_presentation_id,type,file_id,created_at,updated_at,
        deleted_at
    </sql>
</mapper>
