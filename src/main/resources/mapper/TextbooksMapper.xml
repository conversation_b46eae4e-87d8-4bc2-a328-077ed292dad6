<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.TextbooksMapper">

    <select id="getKeypointsByBookIdAndPageNo" resultType="com.joinus.knowledge.model.vo.TextbookPointVO">
        select kp.id,
               kp.name,
               kp.original_name,
               case when kp.exam_point is true then 'exam_point' else 'knowledge_point' end as type,
               kp.sort_no,
               s.section_id,
               s.section_name,
               s.chapter_name
        from math_section_knowledge_points skp, math_knowledge_points kp, view_math_sections s
        where skp.knowledge_point_id = kp.id
          and skp.section_id = s.section_id
          and s.textbook_id = #{bookId}
          and skp.page_index = #{pageNo}
          and kp.deleted_at is null
        union all
        select qt.id,
               qt.name,
               qt.original_name,
               'question_type' as type,
               qt.sort_no,
               s.section_id,
               s.section_name,
               s.chapter_name
        from math_section_question_types sqt, math_question_types qt, view_math_sections s
        where sqt.question_type_id = qt.id
          and sqt.section_id = s.section_id
          and sqt.section_id = s.id
          and s.chapter_id = c.id
          and s.textbook_id = #{bookId}
          and sqt.page_index = #{pageNo}
          and qt.deleted_at is null
    </select>

    <select id="getQuestionTypeByKeypointId" resultType="com.joinus.knowledge.model.vo.TextbookPointVO">
        select qt.id,
               qt.name,
               'question_type' as type,
               qt.sort_no,
               vmqt.section_id,
               vmqt.section_name,
               vmqt.chapter_name
        from math_section_question_types sqt, math_question_types qt, view_math_question_types vmqt
        where sqt.question_type_id = qt.id
          and qt.id = vmqt.question_type_id
          and sqt.page_index = 32
          and qt.id = #{keyPointId}
          and t.id = #{textbookId}
          and sqt.page_index = #{pageNo}
    </select>

    <select id="getKnowledgePointByKeypointId" resultType="com.joinus.knowledge.model.vo.TextbookPointVO">
        select kp.id,
               kp.name,
               case when kp.exam_point is true then 'exam_point' else 'knowledge_point' end as type,
               kp.sort_no,
               vmkp.section_id,
               vmkp.section_name,
               vmkp.chapter_name
        from math_section_knowledge_points skp, math_knowledge_points kp, view_math_knowledge_points vmkp
        where skp.knowledge_point_id = kp.id
          and kp.id = vmkp.knowledge_point_id
          and kp.id = #{keyPointId}
          and vmkp.id = #{textbookId}
          and skp.page_index = #{pageNo}
    </select>

    <select id="listAllKnowledgePointsAndQuestionTypes"
            resultType="com.joinus.knowledge.model.vo.SectionKeypointVO">
        select *
        from (
                 select vmkp.chapter_id,
                        vmkp.chapter_name,
                        vmkp.chapter_sort_no,
                        vmkp.section_id,
                        vmkp.section_name,
                        vmkp.section_sort_no,
                        'knowledgePoint' as type,
                        1 as type_sort,
                        mkp.id           as keypoint_id,
                        mkp.name         as keypoint_name,
                        mkp.original_name as keypoint_original_name,
                        skp.page_index   as page_index,
                        mkp.created_at as created_at,
                        COALESCE(count(distinct mq.id), 0) as question_count
                 from math_textbooks t
                          inner join view_math_knowledge_points vmkp on t.id = vmkp.textbook_id
                          inner join math_section_knowledge_points skp on vmkp.section_id = skp.section_id
                          inner join math_knowledge_points mkp on skp.knowledge_point_id = mkp.id
                          left join math_knowledge_point_questions qkp on mkp.id = qkp.knowledge_point_id
                          left join math_questions mq on qkp.question_id = mq.id and mq.source = 'BOOK'
                 where t.id = #{id} and mkp.exam_point = false
                   and t.deleted_at is null
                   and mkp.deleted_at is null
                   and mq.deleted_at is null
                 group by vmkp.chapter_id,
                          vmkp.chapter_name,
                          vmkp.chapter_sort_no,
                          vmkp.section_id,
                          vmkp.section_name,
                          vmkp.section_sort_no,
                          mkp.id,
                          skp.page_index
                 union
                 select vmqt.chapter_id,
                        vmqt.chapter_name,
                        vmqt.chapter_sort_no,
                        vmqt.section_id,
                        vmqt.section_name,
                        vmqt.section_sort_no,
                        'questionType' as type,
                        2 as type_sort,
                        mqt.id         as keypoint_id,
                        mqt.name       as keypoint_name,
                        mqt.original_name as keypoint_original_name,
                        skp.page_index as page_index,
                        mqt.created_at as created_at,
                        COALESCE(count(distinct mq.id),0) as question_count
                 from math_textbooks t
                          inner join view_math_question_types vmqt on t.id = vmqt.textbook_id
                          inner join math_section_question_types skp on vmqt.section_id = skp.section_id
                          inner join math_question_types mqt on skp.question_type_id = mqt.id
                          left join math_question_type_questions qkp on mqt.id = qkp.question_type_id
                          left join math_questions mq on qkp.question_id = mq.id and mq.source = 'BOOK'
                 where t.id = #{id}
                   and t.deleted_at is null
                   and mqt.deleted_at is null
                   and mq.deleted_at is null
                 group by vmqt.chapter_id,
                          vmqt.chapter_name,
                          vmqt.chapter_sort_no,
                          vmqt.section_id,
                          vmqt.section_name,
                          vmqt.section_sort_no,
                          mqt.id,
                          skp.page_index
                 union
                 select vmkp.chapter_id,
                        vmkp.chapter_name,
                        vmkp.chapter_sort_no,
                        vmkp.section_id,
                        vmkp.section_name,
                        vmkp.section_sort_no,
                        'examPoint' as type,
                        3 as type_sort,
                        mkp.id           as keypoint_id,
                        mkp.name         as keypoint_name,
                        mkp.original_name as keypoint_original_name,
                        skp.page_index   as page_index,
                        mkp.created_at as created_at,
                        COALESCE(count(distinct mq.id), 0) as question_count
                 from math_textbooks t
                          inner join view_math_knowledge_points vmkp on t.id = vmkp.textbook_id
                          inner join math_section_knowledge_points skp on vmkp.section_id = skp.section_id
                          inner join math_knowledge_points mkp on skp.knowledge_point_id = mkp.id
                          left join math_knowledge_point_questions qkp on mkp.id = qkp.knowledge_point_id
                          left join math_questions mq on qkp.question_id = mq.id and mq.source = 'BOOK'
                 where t.id = #{id} and mkp.exam_point = true
                   and t.deleted_at is null
                   and mkp.deleted_at is null
                   and mq.deleted_at is null
                 group by vmkp.chapter_id,
                          vmkp.chapter_name,
                          vmkp.chapter_sort_no,
                          vmkp.section_id,
                          vmkp.section_name,
                          vmkp.section_sort_no,
                          mkp.id,
                          skp.page_index
             )
        order by page_index,chapter_name, section_name, type_sort, created_at
    </select>

</mapper>
