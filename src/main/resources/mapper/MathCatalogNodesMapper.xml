<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathCatalogNodesMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.MathCatalogNodes">
            <id property="id" column="id" />
            <result property="name" column="name" />
            <result property="sortNo" column="sort_no" />
            <result property="parentId" column="parent_id" />
            <result property="idPath" column="id_path" />
            <result property="level" column="level" />
            <result property="textbookId" column="textbook_id" />
            <result property="createdAt" column="created_at" />
            <result property="updatedAt" column="updated_at" />
            <result property="deletedAt" column="deleted_at" />
            <result property="startPage" column="start_page" />
            <result property="endPage" column="end_page" />
    </resultMap>

    <sql id="Base_Column_List">
        id,name,sort_no,parent_id,id_path,level,
        textbook_id,created_at,updated_at,deleted_at,start_page,
        end_page
    </sql>
    <select id="list" resultType="com.joinus.knowledge.model.vo.MathCatalogNodeVO">
        select * from view_math_catalog_nodes
        <where>
            <if test="publisher.value != null">
                and publisher = #{publisher.value}
            </if>
            <if test="grade != null">
                and grade = #{grade}
            </if>
            <if test="semester != null">
                and semester = #{semester}
            </if>
        </where>
    </select>

    <!-- 查询章节列表（用于替代原MathChaptersMapper.list方法） -->
    <select id="listChapters" resultType="com.joinus.knowledge.model.vo.MathChapterVO">
        select mc.id,
               mc.name,
               mc.sort_no as chapter_sort_no,
               te.grade,
               te.publisher,
               te.semester
        from math_catalog_nodes mc
        inner join math_textbooks te on mc.textbook_id = te.id and te.deleted_at is null
        where mc.deleted_at is null
          and mc.level = 1
          and mc.textbook_id is not null
        <if test="name != null and name != ''">
            and mc.name like concat('%', #{name}, '%')
        </if>
        <if test="grade != null">
            and te.grade = #{grade}
        </if>
        <if test="semester != null">
            and te.semester = #{semester}
        </if>
        <if test="publisher != null">
            and te.publisher = #{publisher.value}
        </if>
        order by mc.sort_no
    </select>

    <!-- 根据教材ID查询所有小节（叶节点） -->
    <select id="listAllSectionsByBookId" resultType="com.joinus.knowledge.model.vo.SectionVO">
        select t.id as textbookId,
               t.publisher,
               t.grade,
               t.semester,
               parent.id as chapterId,
               parent.name as chapterName,
               parent.sort_no as chapterSortNo,
               mc.id as sectionId,
               mc.name as sectionName,
               mc.sort_no as sectionSortNo
        from math_catalog_nodes mc
        inner join math_textbooks t on t.id = #{bookId} and t.deleted_at is null
        left join math_catalog_nodes parent on mc.parent_id = parent.id and parent.deleted_at is null
        where mc.deleted_at is null
          and mc.id_path ~ ('*.' || #{bookId}::text || '.*')
          and mc.start_page is not null
          and mc.end_page is not null
        order by parent.sort_no, mc.sort_no
    </select>

    <!-- 查询小节列表（用于替代原MathSectionMapper.list方法） -->
    <select id="listSections" resultType="com.joinus.knowledge.model.vo.MathSectionVO">
        select mc.section_id as id,
               mc.section_name as name
        from view_math_sections mc
        inner join math_textbooks te on mc.textbook_id = te.id and te.deleted_at is null
        <where>
        <if test="name != null and name != ''">
            and mc.section_name like concat('%', #{name}, '%')
        </if>
        <if test="grade != null">
            and mc.grade = #{grade}
        </if>
        <if test="semester != null">
            and mc.semester = #{semester}
        </if>
        <if test="publisher != null">
            and mc.publisher = #{publisher.value}
        </if>
        <if test="chapterName != null and chapterName != ''">
            and mc.chapter_name like concat('%', #{chapterName}, '%')
        </if>
        <if test="chapterId != null">
            and mc.chapter_id = #{chapterId}
        </if>
        </where>
        order by mc.grade, mc.semester, mc.start_page
    </select>

    <!-- 根据页码查询小节 -->
    <select id="getByPageNo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from view_math_catalog_nodes mc
        where mc.deleted_at is null
          and mc.start_page is not null
          and mc.end_page is not null
          and mc.text_book_id = #{textbookId}
        <if test="pageNo != null">
            and #{pageNo} between mc.start_page and mc.end_page
        </if>
        <if test="sectionId != null">
            and mc.id = #{sectionId}
        </if>
        order by mc.sort_no
    </select>

    <!-- 根据小节ID查询关键点 -->
    <select id="listKeypointsById" resultType="com.joinus.knowledge.model.vo.SectionKeypointVO">
        select skp.id,
               skp.section_id as sectionId,
               skp.knowledge_point_id as knowledgePointId,
               mkp.name as knowledgePointName,
               skp.page_index as pageIndex,
               mkp.type as type
        from math_section_knowledge_points skp
        inner join math_knowledge_points mkp on skp.knowledge_point_id = mkp.id
        where skp.section_id = #{sectionId}
        order by skp.page_index
    </select>

    <!-- 根据出版社查询所有小节 -->
    <select id="listAllSectionsByPublisher" resultType="com.joinus.knowledge.model.vo.SectionVO">
        select t.id as textbookId,
               t.publisher,
               t.grade,
               t.semester,
               parent.id as chapterId,
               parent.name as chapterName,
               parent.sort_no as chapterSortNo,
               mc.id as sectionId,
               mc.name as sectionName,
               mc.sort_no as sectionSortNo
        from math_textbooks t
        inner join math_catalog_nodes parent on parent.textbook_id = t.id and parent.deleted_at is null and parent.level = 1
        inner join math_catalog_nodes mc on mc.parent_id = parent.id and mc.deleted_at is null
        where t.subject = '数学'
          and t.publisher = #{publisher.value}
          and mc.start_page is not null
          and mc.end_page is not null
        order by t.id, parent.sort_no, mc.sort_no
    </select>

    <!-- 根据条件查询小节 -->
    <select id="listSectionsByCondition" resultType="com.joinus.knowledge.model.vo.SectionVO">
        select t.id as textbookId,
               t.publisher,
               t.grade,
               t.semester,
               parent.id as chapterId,
               parent.name as chapterName,
               parent.sort_no as chapterSortNo,
               mc.id as sectionId,
               mc.name as sectionName,
               mc.sort_no as sectionSortNo
        from math_textbooks t
        inner join math_catalog_nodes parent on parent.textbook_id = t.id and parent.deleted_at is null and parent.level = 1
        inner join math_catalog_nodes mc on mc.parent_id = parent.id and mc.deleted_at is null
        where t.subject = '数学'
          and t.publisher = #{publisher.value}
          and t.grade = #{grade}
          and t.semester = #{semester}
          and mc.start_page is not null
          and mc.end_page is not null
        order by t.id, parent.sort_no, mc.sort_no
    </select>
    <select id="listByTextbookId" resultType="com.joinus.knowledge.model.entity.MathCatalogNodes"
            parameterType="java.util.UUID">
        select section_id as id,* from view_math_sections where textbook_id = #{textbookId}
    </select>

</mapper>
