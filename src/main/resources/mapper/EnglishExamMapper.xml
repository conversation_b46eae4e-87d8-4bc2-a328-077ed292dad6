<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.EnglishExamMapper">
    <!-- 试卷分页条件查询 -->
    <select id="pageQuery" resultType="com.joinus.knowledge.model.vo.EnglishExamVO">
        select t.id,
               t.name,
               t.source_type sourceType,
               t.type,
               t.year,
               t.area,
               t.school,
               t.grade,
               t.semester,
               t.small_questions_num           questionCount,
               t.created_at  createdAt
        from english_flow_exam t
        where t.deleted_at is null
          and t.status = 2
        <if test="param.name != null and param.name != ''">
            AND t.name LIKE CONCAT('%', #{param.name}, '%')
        </if>
        <if test="param.sourceType != null">
            AND t.source_type = #{param.sourceType}
        </if>
        <if test="param.grade != null">
            AND t.grade = #{param.grade}
        </if>
        <if test="param.semester != null and param.semester != ''">
            AND t.semester = #{param.semester}
        </if>
        <if test="param.year != null">
            AND t.year = #{param.year}
        </if>
        <if test="param.school != null and param.school != ''">
            AND t.school = #{param.school}
        </if>
        <if test="param.type != null and param.type != ''">
            AND t.type = #{param.type}
        </if>
        ORDER BY t.created_at DESC
    </select>

    <!-- 学校下拉列表 -->
    <select id="listSchool" resultType="java.lang.String">
        select distinct t.school
        from english_flow_exam t
        where t.deleted_at is null
        <if test="school != null and school != ''">
            t.school = #{school}
        </if>
        ORDER BY t.school
    </select>
</mapper>
