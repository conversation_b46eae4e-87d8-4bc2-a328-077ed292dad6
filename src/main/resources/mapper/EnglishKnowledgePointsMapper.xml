<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.EnglishKnowledgePointsMapper">
    <!-- 学校下拉列表 -->
    <select id="listByQuestionId" resultType="com.joinus.knowledge.model.entity.EnglishKnowledgePoints">
        select t1.*
        from english_knowledge_points t1
                 inner join english_ai_question_knowledge_points t2 on t1.id = t2.knowledge_point_id
        where t1.deleted_at is null
          and t2.question_id = #{questionId}
    </select>
</mapper>
