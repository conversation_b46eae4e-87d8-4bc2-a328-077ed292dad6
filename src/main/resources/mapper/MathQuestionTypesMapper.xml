<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathQuestionTypesMapper">
    <select id="listByTextbookId" resultType="com.joinus.knowledge.model.entity.MathQuestionType">
        select mqt.* from math_question_types mqt,view_math_question_types vmqt
        where mqt.id = vmqt.question_type_id
          and vmqt.textbook_id = #{textbookId}
    </select>

    <select id="listByQuestionId" resultType="com.joinus.knowledge.model.entity.MathQuestionType">
        select t.* from math_question_types t
                            inner join math_question_type_questions qtm on t.id = qtm.question_type_id
        where  t.deleted_at is null
          and qtm.question_id = #{id}
    </select>
    
    <select id="listByQuestionIds" resultType="com.joinus.knowledge.model.po.MathQuestionTypePO">

        select t.question_type_id      as id,
            vmqt.question_type_name as name,
            vmqt.grade,
            vmqt.semester,
            vmqt.publisher,
            vmqt.chapter_id,
            vmqt.chapter_name,
            vmqt.chapter_sort_no,
            vmqt.section_id,
            vmqt.section_name,
            vmqt.section_sort_no,
            t.question_id,
            vmqt.full_path,
            vmqt.category,
            vmqt.section_id as catalog_node_id
        from math_question_type_questions t
        inner join view_math_question_types vmqt on t.question_type_id = vmqt.question_type_id
        where t.question_id in
              <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
                #{questionId}
              </foreach>
    </select>

    <select id="listByQuestionIdsAndPublisher" resultType="com.joinus.knowledge.model.po.MathQuestionTypePO">
        select t.question_type_id      as id,
        vmqt.question_type_name as name,
        vmqt.grade,
        vmqt.semester,
        vmqt.publisher,
        vmqt.chapter_id,
        vmqt.chapter_name,
        vmqt.chapter_sort_no,
        vmqt.section_id,
        vmqt.section_name,
        vmqt.section_sort_no,
        t.question_id
        from math_question_type_questions t
        inner join view_math_question_types vmqt on t.question_type_id = vmqt.question_type_id
        where t.question_id in
        <foreach collection="questionIds" item="questionId" open="(" separator="," close=")">
            #{questionId}
        </foreach>
        <if test="null != publisher">
            and vmqt.publisher = #{publisher.value}
        </if>
    </select>

    <select id="list" resultType="com.joinus.knowledge.model.vo.MathQuestionTypeVO">
        select distinct vmqt.question_type_id id,
            vmqt.question_type_name name,
            vmqt.grade,
            vmqt.semester,
            vmqt.publisher
        from view_math_question_types vmqt
        <if test="name != null and name != ''">
            and vmqt.question_type_name like concat('%', #{name}, '%')
        </if>
        <if test="grade != null">
            and vmqt.grade = #{grade}
        </if>
        <if test="semester != null">
            and vmqt.semester = #{semester}
        </if>
        <if test="publisher != null">
            and vmqt.publisher = #{publisher}
        </if>
        <if test="chapterId != null">
            and vmqt.chapter_id = #{chapterId}
        </if>
        <if test="chapterName != null and chapterName != ''">
            and vmqt.chapter_name = #{chapterName}
        </if>
        <if test="sectionId != null">
            and ms.id = #{sectionId}
        </if>
        <if test="sectionName != null and sectionName != ''">
            and ms.name = #{sectionName}
        </if>
    </select>

    <select id="listByIds" resultType="com.joinus.knowledge.model.vo.MathQuestionTypeVO">
        select mqt.id as id,
        mqt.name as name,
        vmqt.textbook_id,
        vmqt.publisher,
        vmqt.grade,
        vmqt.semester,
        vmqt.chapter_id,
        vmqt.chapter_name,
        vmqt.chapter_sort_no,
        vmqt.section_id,
        vmqt.section_name,
        vmqt.section_sort_no,
        sqt.page_index,
        vmqt.category
        from math_question_types mqt
        inner join math_section_question_types sqt on mqt.id = sqt.question_type_id
        inner join view_math_question_types vmqt on mqt.id = vmqt.question_type_id
        where mqt.deleted_at is null
             and mqt.id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        order by vmqt.grade,vmqt.semester, vmqt.chapter_sort_no, vmqt.section_sort_no, sqt.page_index
    </select>

    <select id="listByIdsAndPublisher" resultType="com.joinus.knowledge.model.vo.MathQuestionTypeVO">
        select mqt.id as id,
        mqt.name as name,
        vmqt.textbook_id,
        vmqt.publisher,
        vmqt.grade,
        vmqt.semester,
        vmqt.chapter_id,
        vmqt.chapter_name,
        vmqt.chapter_sort_no,
        vmqt.section_id,
        vmqt.section_name,
        vmqt.section_sort_no,
        sqt.page_index
        from math_question_types mqt
        inner join math_section_question_types sqt on mqt.id = sqt.question_type_id
        inner join view_math_question_types vmqt on mqt.id = vmqt.question_type_id
        where mqt.deleted_at is null
        and mqt.id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="publisher != null">
            and vmqt.publisher = #{publisher.value}
        </if>
        <if test="grade != null">
            and vmqt.grade = #{grade}
        </if>
        <if test="semester != null">
            and vmqt.semester = #{semester}
        </if>
        order by vmqt.grade,vmqt.semester, vmqt.chapter_sort_no, vmqt.section_sort_no, sqt.page_index
    </select>

    <select id="listEnableAiQuestionCountByKnowledgePointIds"
            resultType="com.joinus.knowledge.model.vo.MathQuestionTypeVO">
        select mqt.id,
               mqt.name,
               count(distinct mq.id) as enableAiQuestionCount
        from
            math_knowledge_points mkp
                inner join math_knowledge_point_question_types kpqt on mkp.id = kpqt.knowledge_point_id
                inner join math_question_types mqt on kpqt.question_type_id = mqt.id and mqt.deleted_at is null
                left join math_question_type_questions qtq on kpqt.question_type_id = qtq.question_type_id
                left join math_questions mq on qtq.question_id = mq.id and mq.deleted_at is null and mq.enabled = true and mq.source = 'AI'
        where mkp.deleted_at is null
        and mkp.id in
        <foreach collection="knowledgePointIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group by mqt.id, mqt.name
    </select>

    <select id="listBySectionIds" resultType="com.joinus.knowledge.model.vo.MathQuestionTypeVO">
        select vmqt.question_type_id as id,
        vmqt.question_type_name as name,
        vmqt.textbook_id,
        vmqt.publisher,
        vmqt.grade,
        vmqt.semester,
        vmqt.chapter_id,
        vmqt.chapter_name,
        vmqt.chapter_sort_no,
        vmqt.section_id,
        vmqt.section_name,
        vmqt.section_sort_no,
        sqt.page_index
        from view_math_question_types vmqt
        inner join math_section_question_types sqt on vmqt.question_type_id = sqt.question_type_id
        where vmqt.section_id in
        <foreach collection="sectionIds" item="sectionId" open="(" separator="," close=")">
            #{sectionId}
        </foreach>
        order by vmqt.grade, vmqt.semester, vmqt.chapter_sort_no, vmqt.section_sort_no, sqt.page_index
    </select>
    <select id="pageQuery" resultType="com.joinus.knowledge.model.vo.MathQuestionTypeVO">
        select vmqt.question_type_id as id,
        vmqt.question_type_name as name,
        vmqt.question_type_original_name as original_name,
        vmqt.grade,
        vmqt.semester,
        vmqt.publisher,
        vmqt.textbook_id,
        vmqt.full_path,
        vmqt.section_id as catalog_node_id,
        vmqt.category
        from view_math_question_types vmqt
        <where>
        <if test="param.id != null">
            and vmqt.question_type_id = #{param.id}
        </if>
        <if test="param.textbookId != null">
            and vmqt.textbook_id = #{param.textbookId}
        </if>
        <if test="param.publisher != null">
            and vmqt.publisher = #{param.publisher.value}
        </if>
        <if test="param.grade != null">
            and vmqt.grade = #{param.grade}
        </if>
        <if test="param.semester != null">
            and vmqt.semester = #{param.semester}
        </if>
        <if test="param.catalogNodeId != null">
            and vmqt.section_id in (SELECT id
            FROM math_catalog_nodes
            WHERE id_path &lt;@ (
            SELECT id_path
            FROM math_catalog_nodes
            WHERE id = #{param.catalogNodeId})
            AND deleted_at IS NULL)
        </if>
        <if test="param.name != null and param.name != ''">
            and vmqt.question_type_name like '%' || #{param.name} || '%'
        </if>
        <if test="param.originalName != null and param.originalName != ''">
            and vmqt.question_type_original_name like '%' || #{param.originalName} || '%'
        </if>
        <if test="param.category != null and param.category !=''">
            and vmqt.category = #{param.category}
        </if>
        </where>
        order by vmqt.publisher, vmqt.grade, vmqt.semester, vmqt.chapter_sort_no, vmqt.section_sort_no, vmqt.category, vmqt.sort_no
    </select>
</mapper>
