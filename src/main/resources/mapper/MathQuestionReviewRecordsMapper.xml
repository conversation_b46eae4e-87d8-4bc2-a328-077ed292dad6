<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.joinus.knowledge.mapper.MathQuestionReviewRecordsMapper">

    <resultMap id="BaseResultMap" type="com.joinus.knowledge.model.entity.MathQuestionReviewRecords">
            <result property="questionId" column="question_id" />
            <result property="username" column="username" />
            <result property="status" column="status" />
            <result property="remark" column="remark" />
            <result property="createdAt" column="created_at" />
            <result property="reviewedAt" column="reviewed_at" />
            <result property="type" column="type" />
    </resultMap>

    <resultMap id="candidateQuestion" type="com.joinus.knowledge.model.dto.CandidateQuestion">
        <id property="questionId" column="question_id" javaType="java.util.UUID" jdbcType="OTHER"/>
        <collection property="keyPointIds" ofType="java.util.UUID" javaType="java.util.ArrayList">
            <constructor>
                <idArg column="key_point_id" javaType="java.util.UUID" jdbcType="OTHER"/>
            </constructor>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        question_id,username,status,remark,created_at,
        reviewed_at,type
    </sql>
    <select id="getInitialKeyPointStats" resultType="java.util.Map">
        select mkpq.knowledge_point_id as "keyPointId", count(mkpq.knowledge_point_id) as "totalCount",
               count(case when mqrr.status = 'APPROVED' then 1 end) as "successCount"
        from math_questions mq
                 inner join math_knowledge_point_questions mkpq on mq.id = mkpq.question_id
                 inner join view_math_knowledge_points vmkp on mkpq.knowledge_point_id = vmkp.knowledge_point_id
                 inner join math_question_review_records mqrr on mq.id = mqrr.question_id and mqrr.type = #{reviewType}
        where mq.source = 'AI'
          and mq.deleted_at is null
          and vmkp.grade between #{educationalStage.startGrade} and #{educationalStage.endGrade}
        group by mkpq.knowledge_point_id
        union
        select mqtq.question_type_id as "keyPointId", count(mqtq.question_type_id) as "totalCount",
               count(case when mqrr.status = 'APPROVED' then 1 end) as "successCount"
        from math_questions mq
                 inner join math_question_type_questions mqtq on mq.id = mqtq.question_id
                 inner join view_math_question_types vmqt on mqtq.question_type_id = vmqt.question_type_id
                 inner join math_question_review_records mqrr on mq.id = mqrr.question_id and mqrr.type = #{reviewType}
        where mq.source = 'AI'
          and mq.deleted_at is null
          and vmqt.grade between #{educationalStage.startGrade} and #{educationalStage.endGrade}
        group by mqtq.question_type_id
    </select>
    <select id="getAllCandidatesWithKeyPoints" resultMap="candidateQuestion">
        select mkpq.question_id, mkpq.knowledge_point_id as key_point_id from math_questions mq
        inner join math_knowledge_point_questions mkpq on mq.id = mkpq.question_id
        inner join view_math_knowledge_points vmkp on mkpq.knowledge_point_id = vmkp.knowledge_point_id
        where mq.source = 'AI'
          and mq.deleted_at is null
          and vmkp.exam_point = false
        and vmkp.grade between #{educationalStage.startGrade} and #{educationalStage.endGrade}
        and not exists(select 1 from math_question_review_records mqrr
                         where mqrr.question_id = mq.id
                           and mqrr.type = #{reviewType})
        <if test="reviewType == 'SECOND_REVIEW'">
            and exists(select 1 from math_question_review_records mqrrf
            where mqrrf.question_id = mq.id
            and mqrrf.type = 'FIRST_REVIEW'
            and mqrrf.status = 'APPROVED'
            and mqrrf.username != #{username})
        </if>
        <!--union
        select mqtq.question_id, mqtq.question_type_id as key_point_id from math_questions mq
        inner join math_question_type_questions mqtq on mq.id = mqtq.question_id
        inner join view_math_question_types vmqt on mqtq.question_type_id = vmqt.question_type_id
        where mq.source = 'AI'
          and mq.deleted_at is null
        and vmqt.grade between #{educationalStage.startGrade} and #{educationalStage.endGrade}
        and not exists(select 1 from math_question_review_records mqrr
                         where mqrr.question_id = mq.id
                           and mqrr.type = #{reviewType})
        <if test="reviewType == 'SECOND_REVIEW'">
            and exists(select 1 from math_question_review_records mqrrf
            where mqrrf.question_id = mq.id
            and mqrrf.type = 'FIRST_REVIEW'
            and mqrrf.status = 'APPROVED'
            and mqrrf.username != #{username})
        </if>-->
    </select>
    <select id="listReviewRecordsByUser" resultType="com.joinus.knowledge.model.vo.MathQuestionReviewRecordVO">
        select
            mq.id,
            mq.content,
            mq.question_type,
            mq.difficulty,
            mq.source,
            mq.exist_graphics,
            mq.created_at,
            mq.updated_at,
            mqrr.username,
            mqrr.reviewed_at,
            mqrr.type as review_type,
            mqrr.remark,
            mqrr.status
        from math_questions mq
                 inner join math_question_review_records mqrr on mq.id = mqrr.question_id and mqrr.type = #{reviewType}
        where mq.deleted_at is null
            <if test = "username != null and username != ''">
                and mqrr.username = #{username}
            </if>
            <if test = "questionId != null">
                and mqrr.question_id = #{questionId}
            </if>
            <if test = "status != null">
                and mqrr.status = #{status}
            </if>
            <if test="startDate != null">
                and mqrr.reviewed_at &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                and mqrr.reviewed_at &lt;= #{endDate}
            </if>
        order by mqrr.reviewed_at desc nulls first, mqrr.created_at desc
    </select>
    <select id="getUserStatistics" resultType="com.joinus.knowledge.model.vo.MathQuestionReviewUserStatisticsVO">
        select count(case when mqrr.status = 'APPROVED' then 1 end) as approved_count,
               count(case when mqrr.status = 'REJECTED' then 1 end) as rejected_count,
               count(case when mqrr.status = 'PENDING' then 1 end) as pending_count,
               count(*) as total_count
        from math_question_review_records mqrr
                 inner join math_questions mq on mqrr.question_id = mq.id and mq.deleted_at is null
        where mqrr.username = #{username}
          and mqrr.type = #{reviewType}

    </select>
</mapper>
