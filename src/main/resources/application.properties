app.id=edu-knowledge-manage
apollo.bootstrap.enabled=true
apollo.bootstrap.eagerLoad.enabled=true
spring.temporal.start-workers=false
ali.oss.region=cn-beijing
#ali.oss.endpoint=https://oss-cn-beijing.aliyuncs.com
ali.oss.endpoint=https://oss-edu-knowledge-hub.qingyulan.net
ali.oss.accessKeyId=LTAI5tH52JJv1MG3sH8eGWE2
ali.oss.accessKeySecret=******************************
ali.oss.bucketName=edu-knowledge-hub
ali.oss.baseDir=uat/
ali.oss.sts.role-arn=acs:ram::1127886446663093:role/edu-knowledge-hub-role

spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Redis\u914D\u7F6E
spring.data.redis.host=************
spring.data.redis.port=6379
spring.data.redis.database=0
spring.data.redis.password=ijx967111
# Redis\u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09
spring.data.redis.timeout=3000
# \u8FDE\u63A5\u6C60\u6700\u5927\u8FDE\u63A5\u6570
spring.data.redis.lettuce.pool.max-active=8
# \u8FDE\u63A5\u6C60\u6700\u5927\u963B\u585E\u7B49\u5F85\u65F6\u95F4\uFF08\u8D1F\u6570\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.data.redis.lettuce.pool.max-wait=-1
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5927\u7A7A\u95F2\u8FDE\u63A5
spring.data.redis.lettuce.pool.max-idle=8
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5C0F\u7A7A\u95F2\u8FDE\u63A5
spring.data.redis.lettuce.pool.min-idle=0

# OAuth ??????
keycloak.oauth.clients.math-oauth.client-secret=kOpQZQ4rIA2rHd4Bz8AN3KpyCEa5ATVr
keycloak.oauth.clients.math-oauth.redirect-url=http://localhost:8000/quality-review